# 订单金额刷新功能实现文档

## 功能概述

本次实现为 `refreshOrderAmount` 方法添加了手续费刷新逻辑，支持运费和手续费的分离式处理。

## 核心设计思路

### 分离式处理架构

1. **运费刷新**：继续使用原有逻辑，仅处理三方仓订单
2. **手续费刷新**：新增逻辑，处理所有待支付订单
3. **结果合并**：将两种刷新结果合并到统一的返回对象中

### 业务逻辑

- **运费**：与三方仓订单相关，基于配送规则计算
- **手续费**：与订单金额和支付方式相关，所有待支付订单都需要检查
- **非现结支付**：手续费为0
- **批量处理**：支持多个订单号的批量刷新

## 实现细节

### 1. 主方法 `refreshOrderAmount`

```java
@Override
public OrderAmountRefreshVO refreshOrderAmount(OrderAmountRefreshDTO dto) {
    // 1. 处理运费刷新（仅三方仓订单）
    boolean deliveryFeeChanged = processDeliveryFeeRefresh(orderNoList, refreshVO);
    
    // 2. 处理手续费刷新（所有待支付订单）
    boolean serviceFeeChanged = processServiceFeeRefresh(orderNoList, refreshVO);
    
    // 3. 合并结果
    if (deliveryFeeChanged || serviceFeeChanged) {
        refreshVO.setRefreshFlag(true);
    }
    
    return refreshVO;
}
```

### 2. 运费刷新处理 `processDeliveryFeeRefresh`

- 使用原有的 `getRefreshOrder` 方法获取三方仓订单
- 计算新运费并与原运费比较
- 如有变化则调用 `updateDeliveryFee` 更新

### 3. 手续费刷新处理 `processServiceFeeRefresh`

- 查询所有订单号对应的订单
- 筛选出待支付状态的订单
- 遍历每个订单计算新手续费
- 累计原手续费和新手续费总额
- 如有变化则调用 `updateServiceFee` 更新

### 4. 手续费计算 `calculateNewServiceFee`

- 检查支付类型（非现结支付手续费为0）
- 获取租户手续费配置
- 计算订单净金额（排除运费和原手续费）
- 根据支付方式选择费率
- 调用现有的 `doCalculateServiceFee` 方法

## 支持的场景

### 场景1：仅运费变化
- 三方仓订单的运费发生变化
- 手续费保持不变
- 返回：`deliveryFeeRefreshFlag=true, serviceFeeRefreshFlag=false`

### 场景2：仅手续费变化
- 待支付订单的手续费发生变化（如支付方式变更）
- 运费保持不变
- 返回：`deliveryFeeRefreshFlag=false, serviceFeeRefreshFlag=true`

### 场景3：运费和手续费都变化
- 同时存在运费和手续费的变化
- 返回：`deliveryFeeRefreshFlag=true, serviceFeeRefreshFlag=true`

### 场景4：都没有变化
- 运费和手续费都没有变化
- 返回：`refreshFlag=false`

## 返回结果字段

### OrderAmountRefreshVO 新增字段

```java
// 原手续费（所有待支付订单的手续费总和）
private BigDecimal oriServiceFee;

// 新手续费（所有待支付订单的新手续费总和）
private BigDecimal newServiceFee;

// 手续费是否发生变化
private Boolean serviceFeeRefreshFlag;

// 运费是否发生变化（原有字段）
private Boolean deliveryFeeRefreshFlag;
```

## 日志记录

- 记录每个订单的运费变化详情
- 记录每个订单的手续费变化详情
- 记录处理的订单数量和变化统计

## 性能优化

- 无变化时避免数据库更新操作
- 批量查询订单信息
- 分离处理避免不必要的计算

## 测试用例

提供了完整的单元测试，覆盖：
- 仅运费变化场景
- 仅手续费变化场景
- 运费和手续费都变化场景
- 无变化场景
- 多订单批量处理场景
- 非待支付订单处理场景

## 使用示例

```java
OrderAmountRefreshDTO dto = new OrderAmountRefreshDTO();
dto.setOrderNoList(Arrays.asList("ORDER_001", "ORDER_002", "ORDER_003"));

OrderAmountRefreshVO result = orderService.refreshOrderAmount(dto);

if (result.getRefreshFlag()) {
    if (result.getDeliveryFeeRefreshFlag()) {
        System.out.println("运费发生变化: " + result.getOriDeliveryFee() + " -> " + result.getNewDeliveryFee());
    }
    
    if (result.getServiceFeeRefreshFlag()) {
        System.out.println("手续费发生变化: " + result.getOriServiceFee() + " -> " + result.getNewServiceFee());
    }
}
```

## 注意事项

1. **运费刷新**：只处理三方仓订单，保持原有业务逻辑不变
2. **手续费刷新**：处理所有待支付订单，支持批量更新
3. **并发控制**：运费刷新使用Redis锁，手续费刷新依赖数据库事务
4. **异常处理**：各个处理步骤独立，单个失败不影响其他处理
5. **向后兼容**：保持原有API接口不变，新增字段向后兼容
