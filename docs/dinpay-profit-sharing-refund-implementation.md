# 智付分账回退功能实现文档

## 功能概述

在现有汇付分账回退基础上，通过策略模式扩展，实现智付分账回退功能。采用通用化设计，去除厂商前缀，提高代码的可扩展性和可维护性。

## 核心设计思路

### 1. 策略接口重构
- **去除厂商前缀**：将 `HuiFuConfirmRefundResultDTO` 重构为通用的 `ConfirmRefundResultDTO`
- **字段扩展**：在通用DTO中同时包含汇付和智付的特有字段
- **向后兼容**：通过静态方法 `fromHuiFu()` 和 `fromDinPay()` 创建通用结果

### 2. 策略模式扩展
- **汇付策略**：`HuiFuPayStrategy` 调用汇付分账回退接口
- **智付策略**：`DinPayStrategy` 调用智付分账回退接口
- **策略工厂**：根据支付渠道自动选择对应策略

### 3. 渠道自动识别
- 根据 `PaymentDTO.onlinePayChannel` 识别支付渠道
- 汇付渠道：`OnlinePayChannelEnum.HUIFU_PAY.getChannel()` = 1
- 智付渠道：`OnlinePayChannelEnum.DIN_PAY.getChannel()` = 2

## 具体实现

### 1. 通用结果DTO设计

#### ConfirmRefundResultDTO
```java
public class ConfirmRefundResultDTO {
    // ========== 通用字段 ==========
    private String respCode;        // 业务响应码
    private String respDesc;        // 业务响应信息
    private String transStat;       // 交易状态
    private String reqSeqId;        // 请求流水号
    private String merchantId;      // 商户号
    
    // ========== 汇付特有字段 ==========
    private String hfSeqId;         // 汇付全局流水号
    private String huifuId;         // 汇付商户号
    private String loanFlag;        // 是否垫资退款
    // ... 其他汇付字段
    
    // ========== 智付特有字段 ==========
    private String refundOrderNo;   // 分账回退订单号
    private String refundStatus;    // 分账回退状态
    
    // ========== 静态构造方法 ==========
    public static ConfirmRefundResultDTO fromHuiFu(HuiFuConfirmRefundResultDTO huiFuResult);
    public static ConfirmRefundResultDTO fromDinPay(String refundOrderNo, String refundStatus, String respCode, String respDesc);
}
```

### 2. 智付策略实现

#### DinPayStrategy
```java
@Service
public class DinPayStrategy implements PayStrategy {
    
    @Override
    public ConfirmRefundResultDTO confirmRefund(List<BillProfitSharingDTO> billProfitSharingList, Long tenantId) {
        // 1. 构建智付分账回退请求
        UnifiedProfitSharingRefundRequest request = buildDinPayProfitSharingRefundRequest(billProfitSharingList, tenantId);
        
        // 2. 调用智付分账回退接口
        UnifiedProfitSharingRefundResult result = paymentClientService.refundProfitSharing(request);
        
        // 3. 处理结果并转换为通用格式
        return handleDinPayProfitSharingRefundResult(result, request);
    }
}
```

#### 关键实现细节
- **配置获取**：复用 `getDinPayChannelConfig()` 方法
- **单号生成**：格式为 `PSRF + 时间戳 + 随机数`
- **金额转换**：元转分（乘以100）
- **状态映射**：智付状态映射为本地状态

### 3. 渠道识别和策略选择

#### RefundServiceImpl.initiateConfirmRefund()
```java
private void initiateConfirmRefund(RefundDTO refundDTO) {
    // 1. 生成确认退款记录
    List<BillProfitSharingDTO> billProfitSharingDTOS = billProfitSharingService.createConfirmRefundRecord(refundDTO);
    
    // 2. 根据支付渠道选择策略
    String payCode = getPayCodeByRefundDTO(refundDTO);
    
    // 3. 调用对应策略执行分账回退
    ConfirmRefundResultDTO confirmRefundResultDTO = payStrategyFactory.getStrategy(payCode).confirmRefund(billProfitSharingDTOS, refundDTO.getTenantId());
    
    // 4. 处理分账回退结果
    boolean updateStatusResult = billProfitSharingService.handleConfirmRefundRecordResult(confirmRefundResultDTO, billProfitSharingDTOS, refundDTO.getId());
}

private String getPayCodeByRefundDTO(RefundDTO refundDTO) {
    PaymentDTO paymentDTO = paymentService.querySuccessPaymentInfoByOrderId(refundDTO.getOrderId(), refundDTO.getTenantId());
    Integer onlinePayChannel = paymentDTO.getOnlinePayChannel();
    
    if (Objects.equals(onlinePayChannel, OnlinePayChannelEnum.HUIFU_PAY.getChannel())) {
        return PaymentEnums.HF_PAY.getPayCode();
    } else if (Objects.equals(onlinePayChannel, OnlinePayChannelEnum.DIN_PAY.getChannel())) {
        return PaymentEnums.DIN_PAY.getPayCode();
    } else {
        return PaymentEnums.HF_PAY.getPayCode(); // 默认汇付
    }
}
```

### 4. 结果处理重构

#### BillProfitSharingServiceImpl.handleConfirmRefundRecordResult()
```java
@Override
public boolean handleConfirmRefundRecordResult(ConfirmRefundResultDTO confirmRefundResultDTO, List<BillProfitSharingDTO> billProfitSharingDTOList, Long refundId) {
    // 根据结果类型选择不同的处理逻辑
    if (confirmRefundResultDTO.getRefundOrderNo() != null) {
        // 智付分账回退结果处理
        return handleDinPayConfirmRefundResult(confirmRefundResultDTO, billProfitSharingDTOList, refundId);
    } else {
        // 汇付分账回退结果处理
        return handleHuiFuConfirmRefundResult(confirmRefundResultDTO, billProfitSharingDTOList, refundId);
    }
}
```

## 数据映射

### 1. 智付分账回退请求映射
- `refundProfitSharingNo`：分账回退单号（新生成）
- `profitSharingNo`：原分账单号（从 `BillProfitSharing.outTradeNo` 获取）
- `refundSplitRules`：回退规则列表
  - `merchantNo`：`BillProfitSharing.account`（接收方商户号）
  - `amount`：`BillProfitSharing.price * 100`（回退金额，分）
  - `description`：回退描述

### 2. 智付分账回退结果映射
- `refundOrderNo`：分账回退订单号 → `BillProfitSharing.account`
- `refundStatus`：分账回退状态 → 映射为本地状态
- `respCode`：响应码 → `BillProfitSharing.detailId`
- `respDesc`：响应描述 → `BillProfitSharing.failReason`

### 3. 状态映射
```java
智付状态 → 本地状态
SUCCESS → FINISHED (1-分账完成)
DOING   → WAITING  (0-处理中)
FAILED  → FAILED   (2-分账失败)
```

## 业务流程

### 分账回退完整流程
```
RefundServiceImpl.confirmRefund()
├── 状态判断：CONFIRM_REFUND
├── initiateConfirmRefund()
│   ├── createConfirmRefundRecord() // 创建分账回退记录
│   ├── getPayCodeByRefundDTO()     // 识别支付渠道
│   ├── payStrategyFactory.getStrategy() // 获取对应策略
│   ├── strategy.confirmRefund()    // 执行分账回退
│   └── handleConfirmRefundRecordResult() // 处理结果
└── 状态处理和异常处理
```

### 智付分账回退流程
```
DinPayStrategy.confirmRefund()
├── buildDinPayProfitSharingRefundRequest() // 构建请求
├── paymentClientService.refundProfitSharing() // 调用接口
├── handleDinPayProfitSharingRefundResult() // 处理结果
└── 返回 ConfirmRefundResultDTO
```

## 扩展性设计

### 1. 新渠道接入
- 实现 `PayStrategy` 接口
- 在 `PayStrategyFactory` 中注册
- 添加渠道识别逻辑
- 扩展 `ConfirmRefundResultDTO` 字段（如需要）

### 2. 功能扩展
- **分账回退查询**：TODO，参考正向分账查询实现
- **批量分账回退**：支持多个分账单的批量回退
- **状态同步**：定时任务同步分账回退状态

## 优势特点

### 1. 通用化设计
- 去除厂商前缀，提高代码通用性
- 统一的接口和数据结构
- 便于后续渠道扩展

### 2. 策略模式
- 职责分离，每个策略专注自己的业务逻辑
- 易于测试和维护
- 符合开闭原则

### 3. 向后兼容
- 保持现有汇付逻辑不变
- 通过适配器模式兼容旧接口
- 渐进式升级，风险可控

### 4. 数据复用
- 复用现有 `BillProfitSharing` 表结构
- 通过 `businessType` 区分正向和逆向分账
- 最小化数据库变更

## 待实现功能（TODO）

1. **智付分账回退查询**：实现分账回退状态查询功能
2. **定时任务**：处理中状态的分账回退定时查询
3. **监控告警**：分账回退失败的监控和告警
4. **数据统计**：分账回退的数据统计和报表

## 注意事项

1. **金额单位**：智付接口使用分为单位，需要进行单位转换
2. **状态同步**：及时查询和更新分账回退状态
3. **异常处理**：完善异常处理和日志记录
4. **并发控制**：考虑分账回退操作的并发安全性
5. **数据一致性**：确保分账回退记录和退款状态的一致性

## 总结

智付分账回退功能的实现采用了通用化设计和策略模式，在保持向后兼容的同时，为系统增加了新的分账回退能力。通过重构策略接口，去除厂商前缀，提高了代码的可扩展性和可维护性，为后续接入其他支付渠道奠定了良好的基础。
