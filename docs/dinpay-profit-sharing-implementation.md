# 智付分账接入实现文档

## 实现概述

本次实现在现有汇付分账基础上，通过简单的 if-else 渠道判断方式接入智付分账功能，保持代码简洁性和可维护性。

## 核心设计思路

### 1. 渠道区分策略
- 使用 `paymentDTO.getOnlinePayChannel()` 获取支付渠道
- 汇付渠道：`OnlinePayChannelEnum.HUIFU_PAY.getChannel()` = 1
- 智付渠道：`OnlinePayChannelEnum.DIN_PAY.getChannel()` = 2
- 通过 if-else 分支执行不同的分账逻辑

### 2. 数据结构复用
- 复用现有的 `BillProfitSharing` 表结构
- 新增 `profitSharingChannel` 字段区分分账渠道
- 智付分账数据映射：
  - `transactionId` = `payment.transaction_id`（交易流水号）
  - `outTradeNo` = `profitSharingNo`（分账单号）
  - `account` = 智付商户号（接收方）
  - `price` = 分账金额

## 具体实现

### 1. 接口层修改

#### PaymentService 接口新增方法
```java
/**
 * 智付分账
 *
 * @param billProfitSharings 分账项
 * @param paymentDTO 支付信息
 * @param billProfitSharingOrderDTO 分账订单
 */
void dinPayProfitSharing(List<BillProfitSharing> billProfitSharings, PaymentDTO paymentDTO, BillProfitSharingOrderDTO billProfitSharingOrderDTO);
```

### 2. 业务逻辑层修改

#### ProfitSharingBusinessServiceImpl.doProfitSharing()
```java
// 根据渠道调用分账接口
if (Objects.equals(onlinePayChannel, OnlinePayChannelEnum.HUIFU_PAY.getChannel())) {
    // 汇付分账逻辑
    paymentService.huifuProfitSharing(billProfitSharings, huiFuPayment, billProfitSharingOrderDTO);
} else if (Objects.equals(onlinePayChannel, OnlinePayChannelEnum.DIN_PAY.getChannel())) {
    // 智付分账逻辑
    paymentService.dinPayProfitSharing(billProfitSharings, paymentDTO, billProfitSharingOrderDTO);
} else {
    log.warn("订单：[{}]渠道[{}]暂未开通分账能力", orderId, onlinePayChannel);
}
```

### 3. 服务实现层

#### PaymentServiceImpl.dinPayProfitSharing()
核心实现包括：
1. **构建分账请求**：`buildDinPayProfitSharingRequest()`
2. **调用智付接口**：`paymentClientService.profitSharing()`
3. **处理分账结果**：`handleDinPayProfitSharingResult()`
4. **状态查询**：TODO - 立即查询分账状态

## 关键实现细节

### 1. 渠道配置获取
```java
private ChannelConfig getDinPayChannelConfig(Long paymentChannelId) {
    // 参考 DinPaymentTemplate#buildUnifiedQueryRequest 的配置获取方式
    PaymentChannelQueryByIdDTO paymentChannelQueryByIdDTO = paymentChannelFacade.queryPaymentChannelById(paymentChannelId);
    // 构建 ChannelConfig 对象
}
```

### 2. 分账请求构建
```java
private UnifiedProfitSharingRequest buildDinPayProfitSharingRequest(...) {
    // 构建分账规则列表
    List<SplitRule> splitRules = new ArrayList<>();
    for (BillProfitSharing billProfitSharing : billProfitSharings) {
        SplitRule splitRule = SplitRule.builder()
                .merchantNo(billProfitSharing.getAccount()) // 接收方商户号
                .amount(billProfitSharing.getPrice().multiply(new BigDecimal("100")).intValue()) // 分账金额（分）
                .description(billProfitSharing.getDescription()) // 分账描述
                .build();
        splitRules.add(splitRule);
    }
    
    // 构建统一分账请求
    return UnifiedProfitSharingRequest.builder()
            .profitSharingNo(billProfitSharingOrderDTO.getProfitSharingNo())
            .paymentNo(paymentDTO.getPaymentNo())
            .splitRules(splitRules)
            .channelConfig(channelConfig)
            .build();
}
```

### 3. 状态映射
```java
private Integer mapDinPayProfitSharingStatusToLocal(ProfitSharingStatus dinPayStatus) {
    switch (dinPayStatus) {
        case SUCCESS:
            return ProfitSharingResultEnum.FINISHED.getStatus(); // 1-分账完成
        case DOING:
            return ProfitSharingResultEnum.PROCESSING.getStatus(); // 0-处理中
        case FAILED:
            return ProfitSharingResultEnum.FAILED.getStatus(); // 2-分账失败
        default:
            return ProfitSharingResultEnum.WAITING.getStatus(); // 0-待分账
    }
}
```

### 4. 结果处理
- **成功**：更新分账明细状态，设置成功时间
- **失败**：记录失败原因，更新状态为失败
- **处理中**：保持处理中状态，等待后续查询

## 待实现功能（TODO）

### 1. 分账状态查询
```java
// TODO: 实现分账状态查询逻辑
// 调用智付查询接口获取最新状态
// 更新本地分账记录状态
```

### 2. 分账回退
- 分账回退接口调用
- 回退状态查询
- 回退结果处理

### 3. 定时任务查询
- 处理中状态的分账单定时查询
- 状态同步和更新

## 优势特点

### 1. 简单直接
- 使用 if-else 分支，逻辑清晰
- 无需复杂的设计模式
- 易于理解和维护

### 2. 数据兼容
- 复用现有数据结构
- 最小化数据库变更
- 保持业务连续性

### 3. 渐进式实现
- 先实现核心分账功能
- 后续逐步完善查询、回退等功能
- 降低实现风险

### 4. 配置统一
- 使用相同的渠道配置获取方式
- 与智付支付保持一致
- 便于运维管理

## 测试验证

### 1. 单元测试
- 分账请求构建测试
- 状态映射测试
- 异常处理测试

### 2. 集成测试
- 智付分账接口调用测试
- 数据库状态更新测试
- 日志记录验证

### 3. 业务测试
- 不同渠道分账流程测试
- 分账金额计算验证
- 异常场景处理测试

## 注意事项

1. **金额单位**：智付接口使用分为单位，需要进行单位转换
2. **配置获取**：确保智付渠道配置正确设置
3. **错误处理**：完善异常处理和日志记录
4. **状态同步**：及时查询和更新分账状态
5. **并发控制**：考虑分账操作的并发安全性

## 后续扩展

1. **其他渠道**：可按相同模式接入其他支付渠道的分账功能
2. **功能完善**：逐步实现查询、回退等完整功能
3. **性能优化**：根据实际使用情况进行性能调优
4. **监控告警**：完善分账操作的监控和告警机制
