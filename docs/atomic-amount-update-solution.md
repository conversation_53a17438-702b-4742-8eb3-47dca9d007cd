# 原子性金额更新解决方案

## 问题背景

在订单金额刷新过程中，运费变化会影响订单总金额，进而影响手续费的计算。如果分别更新运费和手续费，会导致以下问题：

1. **瞬时金额不一致**：先更新运费，再更新手续费的过程中，存在金额不匹配的瞬间
2. **手续费计算错误**：手续费应该基于包含新运费的订单总金额计算，而不是基于旧的订单金额
3. **并发问题**：多个请求同时修改同一订单时可能产生数据不一致

## 解决方案

### 核心思路：原子性更新

将运费和手续费的更新合并为一个原子操作，确保：
- 运费变化时，手续费基于新的订单金额重新计算
- 整个更新过程在一个事务中完成
- 使用Redis锁确保并发安全

### 实现架构

```
refreshOrderAmount()
├── checkDeliveryFeeChange()     // 检查运费变化（不更新）
├── checkServiceFeeChange()      // 检查手续费变化（不更新）  
└── processAtomicAmountUpdate()  // 原子性更新
    ├── 运费+手续费同时变化 → 合并更新
    ├── 仅运费变化 → 单独更新运费
    └── 仅手续费变化 → 单独更新手续费
```

## 关键改进

### 1. 分离检查和更新逻辑

**之前**：边检查边更新，容易产生不一致
```java
// 错误的方式
updateDeliveryFee();  // 先更新运费
updateServiceFee();   // 再更新手续费（基于错误的订单金额）
```

**现在**：先检查所有变化，再统一更新
```java
// 正确的方式
DeliveryFeeChangeInfo deliveryChange = checkDeliveryFeeChange();
List<ServiceFeeChangeInfo> serviceChanges = checkServiceFeeChange();
processAtomicAmountUpdate(deliveryChange, serviceChanges);
```

### 2. 智能的手续费重新计算

当运费发生变化时，系统会：
1. 识别出该订单同时存在运费和手续费变化
2. 基于新运费重新计算手续费
3. 在一个请求中同时更新运费和手续费

```java
private BigDecimal calculateNewServiceFeeWithNewDeliveryFee(OrderResp orderDTO, BigDecimal newDeliveryFee) {
    // 计算订单金额（使用新运费）
    BigDecimal orderAmount = NumberUtil.sub(orderDTO.getPayablePrice(), orderDTO.getDeliveryFee()); // 减去原运费
    orderAmount = NumberUtil.add(orderAmount, newDeliveryFee); // 加上新运费
    orderAmount = NumberUtil.sub(orderAmount, oriServiceFee); // 减去原手续费
    
    // 基于新的订单金额计算手续费
    return doCalculateServiceFee(orderAmount, feeRate);
}
```

### 3. 并发安全保障

使用Redis分布式锁确保同一订单的更新操作串行化：
```java
String redisKey = RedisKeyEnum.C00007.join(orderDTO.getOrderNo());
RLock lock = redissonClient.getLock(redisKey);
if (!lock.tryLock()) {
    throw new BizException("该订单正在被其他人刷新支付，请稍后重试");
}
```

## 处理场景

### 场景1：仅运费变化
- 检查：运费有变化，手续费无变化
- 处理：直接更新运费

### 场景2：仅手续费变化  
- 检查：运费无变化，手续费有变化
- 处理：直接更新手续费

### 场景3：运费和手续费都变化
- 检查：运费有变化，手续费也有变化
- 处理：**重新计算手续费**（基于新运费），然后原子性更新

### 场景4：运费变化影响手续费
- 检查：运费有变化，手续费计算上无变化
- 处理：**重新计算手续费**（基于新运费），发现实际有变化，原子性更新

## 数据结构

### DeliveryFeeChangeInfo
```java
private static class DeliveryFeeChangeInfo {
    private OrderResp orderDTO;
    private MerchantDeliveryFeeSnapshotResp deliveryFeeSnapshotResp;
    private BigDecimal oriDeliveryFee;
    private BigDecimal newDeliveryFee;
}
```

### ServiceFeeChangeInfo
```java
private static class ServiceFeeChangeInfo {
    private OrderResp orderDTO;
    private BigDecimal oriServiceFee;
    private BigDecimal newServiceFee;
}
```

## 核心方法

### processAtomicAmountUpdate()
负责原子性更新的核心方法：
1. 检查运费变化的订单是否也有手续费变化
2. 如果有，合并更新；如果没有，分别更新
3. 确保所有更新在适当的锁保护下进行

### updateDeliveryFeeAndServiceFee()
同时更新运费和手续费的原子操作：
1. 基于新运费重新计算手续费
2. 构建包含运费和手续费的更新请求
3. 一次性提交更新

## 优势

1. **数据一致性**：消除了瞬时金额不一致的问题
2. **计算准确性**：手续费始终基于正确的订单金额计算
3. **并发安全**：使用分布式锁避免并发冲突
4. **性能优化**：减少了数据库更新次数
5. **业务正确性**：符合"运费变化影响手续费"的业务逻辑

## 测试验证

提供了专门的测试用例验证：
- 原子性更新的正确性
- 金额计算的一致性
- 并发场景下的数据安全性

## 向后兼容

- API接口保持不变
- 返回结果结构保持不变
- 仅内部实现逻辑优化，对调用方透明
