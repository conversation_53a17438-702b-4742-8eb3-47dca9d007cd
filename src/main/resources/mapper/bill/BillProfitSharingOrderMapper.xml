<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.bill.mapper.BillProfitSharingOrderMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.bill.model.po.BillProfitSharingOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="supplier_id" jdbcType="BIGINT" property="supplierId"/>
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="profit_sharing_mode" jdbcType="TINYINT" property="profitSharingMode" />
    <result column="profit_sharing_type" jdbcType="TINYINT" property="profitSharingType"/>
    <result column="retry_num" jdbcType="INTEGER" property="retryNum"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="profit_sharing_no" jdbcType="VARCHAR" property="profitSharingNo"/>
  </resultMap>
  <sql id="Base_Column_List">
    id,
    tenant_id,
    order_id,
    `status`,
    create_time,
    update_time,
    retry_num,
    supplier_id,
    profit_sharing_type,
    profit_sharing_no,
    profit_sharing_mode
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bill_profit_sharing_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bill_profit_sharing_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.bill.model.po.BillProfitSharingOrder" useGeneratedKeys="true">
    insert into bill_profit_sharing_order (tenant_id, order_id, `status`, 
      create_time, update_time)
    values (#{tenantId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{status,jdbcType=TINYINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.bill.model.po.BillProfitSharingOrder" useGeneratedKeys="true">
    insert into bill_profit_sharing_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="profitSharingType != null">
        profit_sharing_type,
      </if>
      <if test="profitSharingNo != null">
        profit_sharing_no,
      </if>
      <if test="profitSharingMode != null">
        profit_sharing_mode,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="supplierId != null">
        #{supplierId,jdbcType=BIGINT},
      </if>
      <if test="profitSharingType != null">
        #{profitSharingType,jdbcType=TINYINT},
      </if>
      <if test="profitSharingNo != null">
        #{profitSharingNo,jdbcType=VARCHAR},
      </if>
      <if test="profitSharingMode != null">
        #{profitSharingMode,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.bill.model.po.BillProfitSharingOrder">
    update bill_profit_sharing_order
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.bill.model.po.BillProfitSharingOrder">
    update bill_profit_sharing_order
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      `status` = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryProfitSharingOrderForProcessIng" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    bill_profit_sharing_order
    where
    status in (2, 5)
  </select>

  <update id="updateStatus">
    update bill_profit_sharing_order
    set status = #{status}
    where id = #{id}
  </update>

  <update id="updateStatusByOrderId">
    update bill_profit_sharing_order
    set status = #{status}
    where tenant_id = #{tenantId}
    and order_id = #{orderId}
    <if test="originStatus != null">
      and status = #{originStatus}
    </if>
  </update>

  <update id="updateStatusById">
    update bill_profit_sharing_order
    set status = #{finalStatus}
    where id = #{id} and status = #{orgStatus}
  </update>

  <select id="queryWaitingProfitSharingOrder" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    bill_profit_sharing_order
    where status in (0,1) and retry_num <![CDATA[ <= ]]> #{retryNum}
    order by id asc
  </select>
  <select id="queryWaitingProfitSharingOrderByOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    bill_profit_sharing_order
    where status in (0,1) and order_id=#{orderId}
  </select>

  <select id="queryByOrderIdAndTenantId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    bill_profit_sharing_order
    where tenant_id = #{tenantId}
    and order_id = #{orderId}
  </select>

  <select id="queryByOrderIdsAndTenantId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    bill_profit_sharing_order
    where tenant_id = #{tenantId}
    and order_id in
    <foreach collection="orderIds" item="id" close=")" open="(" separator=",">
      #{id}
    </foreach>
    order by id asc
  </select>

  <update id="resetStatusAndIncRetryNum">
    update bill_profit_sharing_order
    set status = 0,
        retry_num = retry_num + 1
    where status = 2 and id = #{id}
  </update>

  <select id="queryByTenantAndOrderAndSupplierId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
    from bill_profit_sharing_order
    <where>
      tenant_id = #{tenantId}
      and order_id = #{orderId}
      <if test="supplierId != null">
        and supplier_id = #{supplierId}
      </if>
    </where>
  </select>

  <select id="queryInitStatusOrderIds" resultType="java.lang.Long">
    select distinct order_id
    from bill_profit_sharing_order
    where status = 4
  </select>

  <select id="selectByProfitSharingNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
    from
    bill_profit_sharing_order
    where profit_sharing_no = #{profitSharingNo}
  </select>

  <update id="updateProfitSharingNo">
    update bill_profit_sharing_order
    set profit_sharing_no = #{profitSharingNo}
    where id = #{id}
  </update>
</mapper>