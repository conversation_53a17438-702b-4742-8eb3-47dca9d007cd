package com.cosfo.mall.bill.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.mall.bill.convert.BillProfitSharingConvert;
import com.cosfo.mall.bill.convert.BillProfitSharingOrderConvert;
import com.cosfo.mall.bill.convert.BillProfitSharingSnapshotConvert;
import com.cosfo.mall.bill.mapper.BillProfitSharingMapper;
import com.cosfo.mall.bill.mapper.BillProfitSharingOrderMapper;
import com.cosfo.mall.bill.mapper.BillProfitSharingRuleMapper;
import com.cosfo.mall.bill.mapper.BillProfitSharingSnapshotMapper;
import com.cosfo.mall.bill.model.dto.BillProfitSharingDTO;
import com.cosfo.mall.bill.model.dto.BillProfitSharingOrderDTO;
import com.cosfo.mall.bill.model.dto.BillProfitSharingSnapshotDTO;
import com.cosfo.mall.bill.model.po.BillProfitSharing;
import com.cosfo.mall.bill.model.po.BillProfitSharingOrder;
import com.cosfo.mall.bill.model.po.BillProfitSharingRefundSnapshot;
import com.cosfo.mall.bill.model.po.BillProfitSharingSnapshot;
import com.cosfo.mall.bill.repository.BillProfitSharingRefundSnapshotRepository;
import com.cosfo.mall.bill.service.BillProfitSharingService;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.OnlinePayChannelEnum;
import com.cosfo.mall.common.constants.ProfitSharingBusinessType;
import com.cosfo.mall.common.constants.ProfitSharingOrderStatusEnum;
import com.cosfo.mall.common.constants.ProfitSharingResultEnum;
import com.cosfo.mall.common.context.RefundEnum;
import com.cosfo.mall.common.context.shard.AccountTypeEnum;
import com.cosfo.mall.common.utils.Global;
import com.cosfo.mall.common.utils.TimeUtils;
import com.cosfo.mall.order.mapper.HuiFuPaymentMapper;
import com.cosfo.mall.order.model.dto.HuiFuConfirmRefundResultDTO;
import com.cosfo.mall.order.model.po.HuiFuPayment;
import com.cosfo.mall.payment.mapper.RefundMapper;
import com.cosfo.mall.payment.model.dto.PaymentDTO;
import com.cosfo.mall.payment.model.dto.RefundAcctSplitDetailDTO;
import com.cosfo.mall.payment.model.dto.RefundDTO;
import com.cosfo.mall.payment.service.PaymentService;
import com.cosfo.mall.tenant.service.TenantService;
import com.cosfo.mall.wechat.bean.profitsharing.QueryOrderParams;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/20
 */
@Slf4j
@Service
public class BillProfitSharingServiceImpl implements BillProfitSharingService {
    @Resource
    private BillProfitSharingRuleMapper billProfitSharingRuleMapper;
    @Resource
    private BillProfitSharingSnapshotMapper billProfitSharingSnapshotMapper;
    @Resource
    private BillProfitSharingOrderMapper billProfitSharingOrderMapper;

    @Resource
    private BillProfitSharingMapper billProfitSharingMapper;
    @Lazy
    @Resource
    private PaymentService paymentService;
    @Resource
    private TenantService tenantService;
    @Resource
    private HuiFuPaymentMapper huiFuPaymentMapper;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private BillProfitSharingRefundSnapshotRepository billProfitSharingRefundSnapshotRepository;


    @Override
    public void queryProfitSharingResult() {
        // 查询分账处理中订单
        List<BillProfitSharingOrder> billProfitSharingOrders = billProfitSharingOrderMapper.queryProfitSharingOrderForProcessIng();
        if (CollectionUtils.isEmpty(billProfitSharingOrders)) {
            return;
        }

        billProfitSharingOrders.stream().forEach(billProfitSharingOrder -> {
            try {
                dealProfitSharingResultQuery(billProfitSharingOrder);
            } catch (Exception exception) {
                log.error("查询分账结果失败", exception);
            }
        });
    }

    @Override
    public void updateByPrimaryKeySelective(BillProfitSharing billProfitSharing) {
        billProfitSharingMapper.updateByPrimaryKeySelective(billProfitSharing);
    }

    @Override
    public void batchUpdateBillProFitSharing(BillProfitSharing update, List<Long> sharingIds) {
        if (CollectionUtils.isEmpty(sharingIds)) {
            return;
        }
        billProfitSharingMapper.batchUpdateBillProFitSharing(update, sharingIds);
    }

    @Override
    public List<BillProfitSharingSnapshotDTO> queryOrderProfitSharingRuleSnapshot(Long tenantId, Long orderId) {
        List<BillProfitSharingSnapshot> billProfitSharingSnapshots = billProfitSharingSnapshotMapper.queryByTenantIdAndOrderId(tenantId, orderId);
        List<BillProfitSharingSnapshotDTO> list = billProfitSharingSnapshots.stream().map(BillProfitSharingSnapshot -> {
            BillProfitSharingSnapshotDTO billProfitSharingSnapshotDTO = new BillProfitSharingSnapshotDTO();
            BeanUtils.copyProperties(BillProfitSharingSnapshot, billProfitSharingSnapshotDTO);
            return billProfitSharingSnapshotDTO;
        }).collect(Collectors.toList());
        return list;
    }

    @Override
    public void updateBillProfitSharingSnapshot(BillProfitSharingSnapshotDTO billProfitSharingSnapshotDTO) {
        BillProfitSharingSnapshot billProfitSharingSnapshot = new BillProfitSharingSnapshot();
        BeanUtils.copyProperties(billProfitSharingSnapshotDTO, billProfitSharingSnapshot);
        billProfitSharingSnapshotMapper.updateByPrimaryKeySelective(billProfitSharingSnapshot);
    }

    @Override
    public void saveBillProfitSharing(BillProfitSharing billProfitSharing) {
        billProfitSharingMapper.insertSelective(billProfitSharing);
    }

    /**
     * 查询分账结果查询
     *
     * @param billProfitSharingOrder
     */
    private void dealProfitSharingResultQuery(BillProfitSharingOrder billProfitSharingOrder) {
        List<BillProfitSharing> billProfitSharings = billProfitSharingMapper.querySuccessByTenantIdAndOrderId(billProfitSharingOrder.getTenantId(), billProfitSharingOrder.getOrderId(), ProfitSharingBusinessType.FORWARD_DIRECTION.getCode());
        // 查询分账记录
        if (CollectionUtils.isEmpty(billProfitSharings)) {
            log.error("{}订单没有生成分账单", billProfitSharingOrder.getOrderId());
            // 更新订单分账状态为待分账
            billProfitSharingOrderMapper.updateStatus(billProfitSharingOrder.getId(), ProfitSharingOrderStatusEnum.WAITING.getCode());
            return;
        }
        // 查询支付记录
        PaymentDTO paymentDTO = paymentService.querySuccessPaymentInfoByOrderId(billProfitSharingOrder.getOrderId(), billProfitSharingOrder.getTenantId());
        if (OnlinePayChannelEnum.HUIFU_PAY.getChannel().equals(paymentDTO.getOnlinePayChannel())) {
            // 处理汇付分账结果查询
            HuiFuPayment huiFuPayment = huiFuPaymentMapper.selectOne(new LambdaQueryWrapper<HuiFuPayment>().eq(HuiFuPayment::getPaymentId, paymentDTO.getId()));

            BillProfitSharing billProfitSharing = billProfitSharings.get(NumberConstant.ZERO);
            QueryOrderParams queryOrderParams = new QueryOrderParams();
            queryOrderParams.setOutOrderNo(billProfitSharing.getOutTradeNo());
            queryOrderParams.setOrgReqDate(billProfitSharing.getCreateTime().format(DateTimeFormatter.BASIC_ISO_DATE));
            queryOrderParams.setHuiFuId(huiFuPayment.getHuifuId());
            queryOrderParams.setOrderId(billProfitSharing.getOrderId());
            // 查询汇付分账结果
            BillProfitSharingOrderDTO billProfitSharingOrderDTO = BillProfitSharingOrderConvert.toBillProfitSharingOrderDTO(billProfitSharingOrder);
            paymentService.handleHuiFuProfitSharingOrderResult(queryOrderParams, billProfitSharings, billProfitSharingOrderDTO);
        } else if (OnlinePayChannelEnum.WECHAT_PAY.getChannel().equals(paymentDTO.getOnlinePayChannel())) {
            // 处理微信分账结果查询
            BillProfitSharing billProfitSharing = billProfitSharings.get(NumberConstant.ZERO);
            // 处理查询参数
            QueryOrderParams queryOrderParams = new QueryOrderParams();
            queryOrderParams.setTransactionId(billProfitSharing.getTransactionId());
            queryOrderParams.setOutOrderNo(billProfitSharing.getOutTradeNo());
            paymentService.handleProfitSharingOrderResult(queryOrderParams, billProfitSharings);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<BillProfitSharingDTO> createConfirmRefundRecord(RefundDTO refundDTO) {
        // 查询订单交易确认流水
        List<BillProfitSharing> billProfitSharings = billProfitSharingMapper.querySuccessByTenantIdAndOrderId(refundDTO.getTenantId(), refundDTO.getOrderId(), ProfitSharingBusinessType.FORWARD_DIRECTION.getCode());
        if(CollectionUtils.isEmpty(billProfitSharings)){
            throw new BizException("该订单：" + refundDTO.getOrderId() + "未进行分账");
        }

        // 交易确认请求流水号
        Long afterSaleId = refundDTO.getAfterSaleId();
        List<BillProfitSharingRefundSnapshot> billProfitSharingRefundSnapshots = billProfitSharingRefundSnapshotRepository.queryByAfterSaleId(refundDTO.getTenantId(), afterSaleId);
        if (CollectionUtils.isEmpty(billProfitSharingRefundSnapshots)) {
            throw new BizException("未查询到交易确认退款记录，售后单：" + afterSaleId);
        }

        String orgProfitSharingNo = billProfitSharingRefundSnapshots.get(0).getOrgProfitSharingNo();
        String orgReqSeqId = null;
        String orgReqDate = null;
        BillProfitSharing orgBillProfitSharing = null;
        if (Objects.nonNull(orgProfitSharingNo)) {
            orgBillProfitSharing = billProfitSharings.stream().filter(el -> Objects.equals(el.getOutTradeNo(), orgProfitSharingNo)).findFirst().orElseThrow(() -> new BizException("未查询到交易确认退款记录，售后单：" + afterSaleId));
        } else {
            orgBillProfitSharing = billProfitSharings.get(NumberConstant.ZERO);
        }
        orgReqSeqId = orgBillProfitSharing.getOutTradeNo();
        orgReqDate = TimeUtils.changeDate2String(orgBillProfitSharing.getSuccessTime(), TimeUtils.FORMAT_STRING);
        List<RefundAcctSplitDetailDTO> refundAcctSplitDetailDTOList = refundDTO.getRefundAcctSplitDetailDTOList();
        // 创建交易确认退款记录
        String confirmRefundNo = Global.generateConfirmRefundNo(refundDTO.getAfterSaleId());
        String finalOrgReqSeqId = orgReqSeqId;

        Map<Long, Integer> accountTypeMap = billProfitSharings.stream().filter(el -> Objects.nonNull(el.getAccountType())).collect(Collectors.toMap(BillProfitSharing::getReceiverTenantId, BillProfitSharing::getAccountType, (k1, k2) -> k1));
        List<BillProfitSharing> billProfitSharingList = refundAcctSplitDetailDTOList.stream().map(refundAcctSplitDetailDTO -> {
            BillProfitSharing billProfitSharing = new BillProfitSharing();
            billProfitSharing.setTenantId(refundDTO.getTenantId());
            billProfitSharing.setReceiverTenantId(refundAcctSplitDetailDTO.getAcctSplitTenantId());
            billProfitSharing.setOrderId(refundDTO.getOrderId());
            billProfitSharing.setType("MERCHANT_ID");
            billProfitSharing.setAccount(refundAcctSplitDetailDTO.getHuifuId());
            billProfitSharing.setBusinessType(ProfitSharingBusinessType.REVERSE.getCode());
            // 请求流水号
            billProfitSharing.setOutTradeNo(confirmRefundNo);
            // 原交易请求流水号
            billProfitSharing.setTransactionId(finalOrgReqSeqId);
            // 交易确认退款金额
            billProfitSharing.setPrice(refundAcctSplitDetailDTO.getDivAmt().negate());
            billProfitSharing.setStatus(ProfitSharingResultEnum.WAITING.getStatus());
            billProfitSharing.setAfterSaleId(refundDTO.getAfterSaleId());
            billProfitSharing.setAccountType(accountTypeMap.getOrDefault(refundAcctSplitDetailDTO.getAcctSplitTenantId(), AccountTypeEnum.getByTenantId(refundAcctSplitDetailDTO.getAcctSplitTenantId())));
            return billProfitSharing;
        }).collect(Collectors.toList());
        billProfitSharingMapper.saveBatch(billProfitSharingList);

        // 乐观更新为交易确认退款请求Id
        int result = refundMapper.updateConfirmRefundReqIdCas(refundDTO.getId(), refundDTO.getConfirmRefundReqId(), confirmRefundNo);
        if (result <= 0) {
            throw new BizException("创建交易确认退款流水失败，售后单：" + refundDTO.getAfterSaleId());
        }

        List<BillProfitSharingDTO> billProfitSharingDTOS = BillProfitSharingConvert.INSTANCE.toBillProfitSharingDTO(billProfitSharingList);
        String finalOrgReqDate = orgReqDate;
        billProfitSharingDTOS.stream().forEach(billProfitSharingDTO -> billProfitSharingDTO.setOrgReqDate(finalOrgReqDate));
        return billProfitSharingDTOS;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean handleConfirmRefundRecordResult(HuiFuConfirmRefundResultDTO huiFuConfirmRefundResultDTO, List<BillProfitSharingDTO> billProfitSharingDTOList, Long refundId) {
        ProfitSharingResultEnum status = ProfitSharingResultEnum.getByHuiFuCode(huiFuConfirmRefundResultDTO.getTransStat());
        List<Long> profitSharingIds = billProfitSharingDTOList.stream().map(BillProfitSharingDTO::getId).collect(Collectors.toList());
        BillProfitSharing billProfitSharing = new BillProfitSharing();
        billProfitSharing.setStatus(status.getStatus());
        billProfitSharing.setWxOrderId(huiFuConfirmRefundResultDTO.getHfSeqId());
        billProfitSharing.setSuccessTime(new Date());
        billProfitSharing.setDetailId(huiFuConfirmRefundResultDTO.getRespCode());
        billProfitSharing.setFailReason(huiFuConfirmRefundResultDTO.getRespDesc());
        // 更新 bill_profit_sharing表
        billProfitSharingMapper.batchUpdateBillProFitSharing(billProfitSharing,profitSharingIds);
        // 如果成功 更新refund status为100, 次数变为0
        Integer refundStatus = null;
        refundStatus = ProfitSharingResultEnum.FINISHED.equals(status) ? RefundEnum.Status.CREATE_REFUND.getStatus() : ProfitSharingResultEnum.FAILED.equals(status) ? RefundEnum.Status.CONFIRM_REFUND.getStatus() : null;
        if(Objects.nonNull(refundStatus)){
            int size = refundMapper.updateStatusCas(refundId, RefundEnum.Status.IN_CONFIRM_REFUND.getStatus(), refundStatus);
            return size > NumberConstant.ZERO;
        }

        return true;
    }

    @Override
    public List<BillProfitSharingDTO> queryConfirmResultRecord(Long tenantId, String confirmRefundReqId) {
        List<BillProfitSharing> billProfitSharings = billProfitSharingMapper.queryByTenantIdAndOutTradeNo(tenantId, confirmRefundReqId, ProfitSharingBusinessType.REVERSE.getCode());
        if(CollectionUtils.isEmpty(billProfitSharings)){
            return Collections.emptyList();
        }

        return BillProfitSharingConvert.INSTANCE.toBillProfitSharingDTO(billProfitSharings);
    }

    @Override
    public List<BillProfitSharingSnapshotDTO> queryByProfitSharingNo(Long tenantId, String profitSharingNo) {
        List<BillProfitSharingSnapshot> billProfitSharingSnapshotDTOS = billProfitSharingSnapshotMapper.queryByProfitSharingNo(tenantId, profitSharingNo);
        return BillProfitSharingSnapshotConvert.toDTOList(billProfitSharingSnapshotDTOS);
    }

    @Override
    public void batchSaveBillProfitSharing(List<BillProfitSharing> billProfitSharingList) {
        billProfitSharingMapper.saveBatch(billProfitSharingList);
    }
}


