package com.cosfo.mall.bill.service.impl;

import com.cosfo.mall.bill.service.BillSettlementService;
import com.cosfo.mall.bill.service.ProfitSharingBusinessService;
import com.cosfo.mall.order.service.OrderItemFeeTransactionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: George
 * @date: 2024-08-22
 **/
@Service
@Slf4j
public class BillSettlementServiceImpl implements BillSettlementService {

    @Resource
    private ProfitSharingBusinessService profitSharingBusinessService;
    @Resource
    private OrderItemFeeTransactionService orderItemFeeTransactionService;
    @Resource
    private PlatformTransactionManager transactionManager;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void generateSettlements(Long tenantId, List<Long> orderIds) {
        // 生成分账明细
        profitSharingBusinessService.generateBillProfitSharingSnapshots(tenantId, orderIds);
        // 生成订单费用相关
        orderItemFeeTransactionService.generateOrderItemFee(orderIds);
        // 如果是实时分账，立马进行分账
        try {
            profitSharingBusinessService.profitSharingByOrderIdsRightNow(tenantId, orderIds);
        } catch (Exception e) {
            log.error("实时分账异常,tenantId:{},orderIds:{}", tenantId, orderIds, e);
        }
    }
}
