package com.cosfo.mall.common.context;

import com.cosfo.mall.common.constants.OnlinePayChannelEnum;
import com.cosfo.mall.payment.strategy.impl.*;
import com.cosfo.mall.payment.template.CombinedPayment;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @desc 支付方式枚举对象
 * <AUTHOR>
 * @date 2023/3/16 11:15
 */
@Getter
@AllArgsConstructor
public enum PaymentEnums {

    /**
     * 微信支付
     */
    WX_PAY(0, "wei_xin_pay", WeiXinPayStrategy.class.getSimpleName()),
    /**
     * 汇付支付
     */
    HF_PAY(1, "hui_fu_pay", HuiFuPayStrategy.class.getSimpleName()),
    /**
     * 账期支付
     */
    BILL_PAY(2, "bill_pay", BillPayStrategy.class.getSimpleName()),
    /**
     * 余额支付
     */
    BALANCE_PAY(3,"balance_pay", BalancePayStrategy.class.getSimpleName()),
    /**
     * 线下支付
     */
    OFFLINE_PAY(4,"offline_pay", OfflinePayStrategy.class.getSimpleName()),

    /**
     * 非现金支付
     */
    NON_CASH_PAY(5, "non_cash_pay", NonCashPayStrategy.class.getSimpleName()),
    /**
     * 组合支付
     */
    COMBINED_PAY(6, "combined_pay", CombinedPayStrategy.class.getSimpleName()),

    /**
     * 智付支付
     */
    DIN_PAY(7, "din_pay", DinPayStrategy.class.getSimpleName())
    ;

    /**
     * 支付类型
     */
    private Integer payType;

    /**
     * 支付 code
     */
    private String payCode;

    /**
     * 描述
     */
    private String beanName;

    /**
     *
     * @param payType 支付类型 1、线上支付 2、账期 3、余额支付
     * @param onlinePayChannel 支付渠道 0、微信 1、汇付
     * @return
     */
    public static String getPayCode(Integer payType, Integer onlinePayChannel) {
        if (Objects.equals(BILL_PAY.getPayType(), payType)) {
            return BILL_PAY.getPayCode();
        }

        if (Objects.equals(BALANCE_PAY.getPayType(), payType)) {
            return BALANCE_PAY.getPayCode();
        }
        if (Objects.equals(OnlinePayChannelEnum.HUIFU_PAY.getChannel(), onlinePayChannel)) {
            return HF_PAY.getPayCode();
        } else {
            return WX_PAY.getPayCode();
        }
    }
}
