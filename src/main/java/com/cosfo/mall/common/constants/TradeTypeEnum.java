package com.cosfo.mall.common.constants;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @date : 2022/12/19 16:54
 */
@Getter
@AllArgsConstructor
public enum TradeTypeEnum {

    /**
     * 微信小程序（直连）
     */
    JSAPI(1, "JSAPI", "微信直连"),

    /**
     * 微信公众号
     */
    T_JSAPI(1, "T_JSAPI", "汇付-公众号微信"),

    /**
     * 微信小程序
     */
    T_MINIAPP(1, "T_MINIAPP", "汇付-小程序微信"),

    /**
     * 微信小程序插件
     */
    HF_WECHAT_PLUGIN(1, "HF_WECHAT_PLUGIN", "汇付-小程序插件"),

    /**
     * 账期
     */
    BILL(2, "BILL", "账期"),

    /**
     * 余额
     */
    BALANCE(3, "BALANCE", "余额"),

    /**
     * 支付宝正扫
     */
    A_NATIVE(4, "A_NATIVE", "汇付-支付宝"),


    /**
     * 无需支付
     */
    ZERO_PRICE(5, "ZERO_PRICE", "无需支付"),

    /**
     * 线下支付
     */
    OFFLINE_PAY(6, "OFFLINE_PAY", "线下支付"),

    /**
     * 非现金支付
     */
    NON_CASH_PAY(7, "NON_CASH_PAY", "非现金支付"),

    /**
     * 组合支付
     */
    COMBINED_PAY(8, "COMBINED_PAY", "组合支付"),

    /**
     * 智付公众号微信
     */
    DIN_H5_WECHAT_PAY(1, "WXPAY(PUBLIC)", "智付-公众号微信"),

    /**
     * 智付公众号支付宝
     */
    DIN_H5_ALI_PAY(4, "ALIPAY(PUBLIC)", "智付-公众号支付宝"),
    ;


    /**
     * 状态类型编码
     */
    private Integer payType;
    /**
     * 状态类型描述
     */
    private String desc;

    /**
     * 详细描述
     */
    private String detailDesc;

    /**
     * 本地支付的交易类型
     *
     * @return
     */
    public static List<String> getNativeTradeType() {
        return Lists.newArrayList(BILL.desc, BALANCE.desc, ZERO_PRICE.desc, OFFLINE_PAY.desc, NON_CASH_PAY.desc);
    }

    /**
     * 外部支付的交易类型
     *
     * @return
     */
    public static List<String> getExternalTradeType() {
        return Lists.newArrayList(JSAPI.desc, T_JSAPI.desc, T_MINIAPP.desc, A_NATIVE.desc, HF_WECHAT_PLUGIN.desc);
    }

    /**
     * 根据tradeType返回支付类型
     * @param desc
     * @return
     */
    public static Integer getPayTypeByTradeType(String desc) {
        for (TradeTypeEnum type : values()) {
            if (type.getDesc().equals(desc)) {
                return type.getPayType();
            }
        }
        return null;
    }

    public static String getDetailDescByTradeType(String desc) {
        for (TradeTypeEnum type : values()) {
            if (type.getDesc().equals(desc)) {
                return type.getDetailDesc();
            }
        }
        return null;
    }
}
