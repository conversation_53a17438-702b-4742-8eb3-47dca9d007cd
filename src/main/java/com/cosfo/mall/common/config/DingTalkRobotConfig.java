package com.cosfo.mall.common.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
@Data
public class DingTalkRobotConfig {

    @Value(value = "${dingtalk.profitsharing.notify.url}")
    private String url;

    @Value(value = "${dingtalk.profitsharing.notify.secret}")
    private String secret;
}
