package com.cosfo.mall.merchant.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-08-29
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MerchantServiceFeeConfigDTO {

    /**
     * 微信费率
     */
    private BigDecimal wechatFeeRate;

    /**
     * 支付宝费率
     */
    private BigDecimal alipayFeeRate;

}
