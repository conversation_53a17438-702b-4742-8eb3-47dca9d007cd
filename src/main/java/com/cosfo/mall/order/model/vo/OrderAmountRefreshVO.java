package com.cosfo.mall.order.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: monna.chen
 * @Date: 2023/10/16 15:18
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderAmountRefreshVO implements Serializable {
    private static final long serialVersionUID = 3823137924085681719L;

    /**
     * 【已废弃】转由serviceFeeRefreshFlag、deliveryFeeRefreshFlag控制
     * 是否发生变化
     * true/1- 订单金额已更新； false/0 - 订单金额未发生变化
     */
    @Deprecated
    private boolean refreshFlag;

    /**
     * 服务费是否发生变化
     */
    private Boolean serviceFeeRefreshFlag;

    /**
     * 运费是否发生变化
     */
    private Boolean deliveryFeeRefreshFlag;

    /**
     * 原运费
     */
    private BigDecimal oriDeliveryFee;

    /**
     * 更新后运费
     */
    private BigDecimal newDeliveryFee;

    /**
     * 更新后订单金额
     */
    private BigDecimal orderAmount;
}
