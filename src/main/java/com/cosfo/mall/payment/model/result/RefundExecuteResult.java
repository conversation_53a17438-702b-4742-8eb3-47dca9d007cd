package com.cosfo.mall.payment.model.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2023-09-14
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RefundExecuteResult {

    /**
     * true 成功 false 失败
     */
    private boolean isSuccess;

    /**
     * 退款状态码
     */
    private String code;
}
