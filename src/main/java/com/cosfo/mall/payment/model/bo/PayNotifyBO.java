package com.cosfo.mall.payment.model.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @author: George
 * @date: 2024-08-19
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PayNotifyBO {

    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 支付单id
     */
    private Long paymentId;
    /**
     * 支付单号
     */
    private String paymentNo;
    /**
     * 交易类型
     */
    private String tradeType;
    /**
     * 订单id
     */
    private List<Long> orderIds;
}
