package com.cosfo.mall.payment.model.request;

import com.cosfo.mall.wechat.bean.base.Amount;
import com.cosfo.mall.wechat.bean.base.Payer;
import lombok.Data;

/**
 * @description: 微信支付请求对象
 * @author: <PERSON>
 * @date: 2023-08-31
 **/
@Data
public class WechatPaymentRequest {

    /**
     * appId
     */
    private String appid;
    /**
     * 商户id
     */
    private String mchid;
    /**
     * 支付描述
     */
    private String description;
    /**
     * 外部单号
     */
    //@JsonProperty("out_trade_no")
    private String out_trade_no;
    /**
     * 过期时间
     */
    //@JsonProperty("time_expire")
    private String time_expire;
    /**
     * 附件
     */
    private String attach;
    /**
     * 回调地址
     */
    //@JsonProperty("notify_url")
    private String notify_url;
    /**
     * 商品标签
     */
    private String goods_tag;
    /**
     * 金额
     */
    private Amount amount;
    /**
     * 支付人
     */
    private Payer payer;
}
