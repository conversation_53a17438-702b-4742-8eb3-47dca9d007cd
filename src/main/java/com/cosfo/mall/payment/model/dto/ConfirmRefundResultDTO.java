package com.cosfo.mall.payment.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * 通用分账回退结果DTO
 * 支持汇付和智付等多种渠道
 *
 * @author: AI Assistant
 * @date: 2025-01-04
 */
@Data
public class ConfirmRefundResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    // ========== 通用字段 ==========

    /**
     * 业务响应码
     */
    private String respCode;

    /**
     * 业务响应信息
     */
    private String respDesc;

    /**
     * 交易状态
     */
    private String transStat;

    /**
     * 请求日期
     */
    private String reqDate;

    /**
     * 请求流水号
     */
    private String reqSeqId;

    /**
     * 商户号
     */
    private String merchantId;

    /**
     * 原交易请求流水号
     */
    private String orgReqSeqId;

    // ========== 汇付特有字段 ==========

    /**
     * 全局流水号（汇付）
     */
    @JSONField(name = "hf_seq_id")
    private String hfSeqId;

    /**
     * 汇付商户号
     */
    @JSONField(name = "huifu_id")
    private String huifuId;

    /**
     * 是否垫资退款（汇付）
     */
    @JSONField(name = "loan_flag")
    private String loanFlag;

    /**
     * 垫资承担者（汇付）
     */
    @JSONField(name = "loan_undertaker")
    private String loanUnderTaker;

    /**
     * 垫资账户类型（汇付）
     */
    @JSONField(name = "loan_acct_type")
    private String loanAcctType;

    /**
     * 待确认总金额（汇付）
     */
    @JSONField(name = "unconfirm_amt")
    private String unConfirmAmt;

    /**
     * 已确认总金额（汇付）
     */
    @JSONField(name = "confirmed_amt")
    private String confirmedAmt;

    /**
     * 支付交易业务请求时间（汇付）
     */
    @JSONField(name = "org_req_date")
    private String orgReqDate;

    /**
     * 支付交易汇付全局流水号（汇付）
     */
    @JSONField(name = "org_hf_seq_id")
    private String orgHfSeqId;

    // ========== 智付特有字段 ==========

    /**
     * 分账回退订单号（智付）
     */
    private String refundOrderNo;

    /**
     * 分账回退状态（智付）
     */
    private String refundStatus;

    // ========== 构造方法 ==========

    /**
     * 从汇付结果创建通用结果
     */
    public static ConfirmRefundResultDTO fromHuiFu(com.cosfo.mall.order.model.dto.HuiFuConfirmRefundResultDTO huiFuResult) {
        if (huiFuResult == null) {
            return null;
        }

        ConfirmRefundResultDTO result = new ConfirmRefundResultDTO();
        // 通用字段映射
        result.setRespCode(huiFuResult.getRespCode());
        result.setRespDesc(huiFuResult.getRespDesc());
        result.setTransStat(huiFuResult.getTransStat());
        result.setReqDate(huiFuResult.getReqDate());
        result.setReqSeqId(huiFuResult.getReqSeqId());
        result.setMerchantId(huiFuResult.getHuifuId());
        result.setOrgReqSeqId(huiFuResult.getOrgReqSeqId());

        // 汇付特有字段
        result.setHfSeqId(huiFuResult.getHfSeqId());
        result.setHuifuId(huiFuResult.getHuifuId());
        result.setLoanFlag(huiFuResult.getLoanFlag());
        result.setLoanUnderTaker(huiFuResult.getLoanUnderTaker());
        result.setLoanAcctType(huiFuResult.getLoanAcctType());
        result.setUnConfirmAmt(huiFuResult.getUnConfirmAmt());
        result.setConfirmedAmt(huiFuResult.getConfirmedAmt());
        result.setOrgReqDate(huiFuResult.getOrgReqDate());
        result.setOrgHfSeqId(huiFuResult.getOrgHfSeqId());

        return result;
    }

    /**
     * 从智付结果创建通用结果
     */
    public static ConfirmRefundResultDTO fromDinPay(String refundOrderNo, String refundStatus, String respCode, String respDesc) {
        ConfirmRefundResultDTO result = new ConfirmRefundResultDTO();
        
        // 通用字段映射
        result.setRespCode(respCode);
        result.setRespDesc(respDesc);
        result.setTransStat(refundStatus); // 智付的refundStatus映射为transStat
        
        // 智付特有字段
        result.setRefundOrderNo(refundOrderNo);
        result.setRefundStatus(refundStatus);

        return result;
    }
}
