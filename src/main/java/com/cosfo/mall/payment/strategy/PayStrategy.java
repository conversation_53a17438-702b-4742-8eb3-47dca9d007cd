package com.cosfo.mall.payment.strategy;


import com.cosfo.mall.bill.model.dto.BillProfitSharingDTO;
import com.cosfo.mall.order.model.dto.HuiFuAcctInfoDTO;
import com.cosfo.mall.order.model.dto.HuiFuConfirmRefundResultDTO;
import com.cosfo.mall.payment.model.dto.*;
import com.cosfo.mall.payment.model.po.Payment;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 支付策略
 * @date 2023/3/16 11:07
 */
public interface PayStrategy {

    /**
     * 发起交易确认退款
     *
     * @param billProfitSharingList 分账回退明细列表
     * @param tenantId 租户ID
     * @return 分账回退结果
     */
    default ConfirmRefundResultDTO confirmRefund(List<BillProfitSharingDTO> billProfitSharingList, Long tenantId){
        return new ConfirmRefundResultDTO();
    }
}
