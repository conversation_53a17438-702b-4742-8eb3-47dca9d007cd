package com.cosfo.mall.payment.strategy.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.mall.bill.model.dto.BillProfitSharingDTO;
import com.cosfo.mall.bill.model.po.BillProfitSharing;
import com.cosfo.mall.common.config.HuiFuConfig;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.utils.Global;
import com.cosfo.mall.common.utils.TimeUtils;
import com.cosfo.mall.order.mapper.HuiFuPaymentMapper;
import com.cosfo.mall.order.model.dto.*;
import com.cosfo.mall.order.model.po.HuiFuPayment;
import com.cosfo.mall.payment.model.dto.*;
import com.cosfo.mall.payment.service.PaymentService;
import com.cosfo.mall.payment.strategy.PayStrategy;
import com.cosfo.mall.tenant.mapper.TenantAuthConnectionMapper;
import com.cosfo.mall.tenant.model.dto.TenantAuthConnectionDTO;
import com.cosfo.mall.tenant.model.po.TenantAuthConnection;
import com.cosfo.mall.tenant.service.TenantAuthConnectionService;
import com.cosfo.mall.wechat.api.HuiFuApi;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 支付策略 - 汇付
 * @date 2023/3/16 11:12
 */
@Service
@Slf4j
public class HuiFuPayStrategy implements PayStrategy {

    @Lazy
    @Resource
    private PaymentService paymentService;
    @Resource
    private HuiFuPaymentMapper huiFuPaymentMapper;
    @Resource
    private HuiFuConfig huiFuConfig;
    @Value("${notify-domain}")
    private String notifyDomain;
    @Resource
    private TenantAuthConnectionService tenantAuthConnectionService;
    @Resource
    private TenantAuthConnectionMapper tenantAuthConnectionMapper;

    @Override
    public ConfirmRefundResultDTO confirmRefund(List<BillProfitSharingDTO> billProfitSharingList, Long tenantId) {
        // 根据支付单查询历史支付配置
        BillProfitSharingDTO billProfitSharingDTO = billProfitSharingList.get(NumberConstant.ZERO);
        PaymentDTO paymentDTO = paymentService.querySuccessByOrderId(billProfitSharingDTO.getTenantId(), billProfitSharingDTO.getOrderId());
        HuiFuPayment huiFuPayment = huiFuPaymentMapper.selectOne(new LambdaQueryWrapper<HuiFuPayment>().eq(HuiFuPayment::getPaymentId, paymentDTO.getId()));
        TenantAuthConnection tenantAuthConnection = tenantAuthConnectionService.queryHistoryPayConfig(paymentDTO.getTenantId(), huiFuPayment.getHuifuId(),null);
        HuiFuConfirmRefundRequestDTO huiFuConfirmRefundRequestDTO = buildHuiFuConfirmRefundRequestDTO(billProfitSharingList, tenantAuthConnection);
        // 发起交易确认退款请求
        HuiFuConfirmRefundResultDTO huiFuConfirmRefundResultDTO = HuiFuApi.huiFuConfirmRefund(huiFuConfirmRefundRequestDTO, tenantAuthConnection, huiFuConfig);
        // 转换为通用结果
        return ConfirmRefundResultDTO.fromHuiFu(huiFuConfirmRefundResultDTO);
    }

    /**
     * 构建汇付交易确认退款请求参数
     *
     * @param billProfitSharingList
     * @param tenantAuthConnection
     * @return
     */
    private HuiFuConfirmRefundRequestDTO buildHuiFuConfirmRefundRequestDTO(List<BillProfitSharingDTO> billProfitSharingList, TenantAuthConnection tenantAuthConnection){
        HuiFuConfirmRefundRequestDTO huiFuConfirmRefundRequestDTO = new HuiFuConfirmRefundRequestDTO();
        // 按huifuId分组
        Map<String, List<BillProfitSharingDTO>> billProfitSharingMap = billProfitSharingList.stream().collect(Collectors.groupingBy(BillProfitSharing::getAccount));
        List<AcctInfoDTO> acctInfoDTOS = billProfitSharingMap.values().stream().map(billProfitSharings -> {
            BillProfitSharing profitSharing = billProfitSharings.get(NumberConstant.ZERO);
            BigDecimal profitSharingPrice = billProfitSharings.stream().map(BillProfitSharing::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            AcctInfoDTO acctInfoDTO = new AcctInfoDTO();
            acctInfoDTO.setHuifu_id(profitSharing.getAccount());
            acctInfoDTO.setDiv_amt(String.format("%.2f", profitSharingPrice.negate()));
            return acctInfoDTO;
        }).collect(Collectors.toList());
        // 分账对象
//        List<AcctInfoDTO> acctInfoDTOS = billProfitSharingList.stream().map(billProfitSharing -> {
//            AcctInfoDTO acctInfoDTO = new AcctInfoDTO();
//            acctInfoDTO.setHuifu_id(billProfitSharing.getAccount());
//            acctInfoDTO.setDiv_amt(String.format("%.2f", billProfitSharing.getPrice().negate()));
//            return acctInfoDTO;
//        }).collect(Collectors.toList());
        Map<String, List<AcctInfoDTO>> map = new HashMap<>();
        map.put("acct_infos", acctInfoDTOS);
        huiFuConfirmRefundRequestDTO.setAcctSplitBunch(JSONObject.toJSONString(map));

        BillProfitSharingDTO billProfitSharingDTO = billProfitSharingList.get(NumberConstant.ZERO);
        huiFuConfirmRefundRequestDTO.setReqDate(TimeUtils.changeDate2String(new Date(), TimeUtils.FORMAT_STRING));
        huiFuConfirmRefundRequestDTO.setReqSeqId(billProfitSharingDTO.getOutTradeNo());
        huiFuConfirmRefundRequestDTO.setHuiFuId(tenantAuthConnection.getHuifuId());
        huiFuConfirmRefundRequestDTO.setOrgReqDate(billProfitSharingDTO.getOrgReqDate());
        huiFuConfirmRefundRequestDTO.setOrgReqSeqId(billProfitSharingDTO.getTransactionId());
        return huiFuConfirmRefundRequestDTO;
    }
}
