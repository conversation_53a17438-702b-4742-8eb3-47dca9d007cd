package com.cosfo.mall.payment.strategy.impl;

import com.cosfo.mall.bill.model.dto.BillProfitSharingDTO;
import com.cosfo.mall.payment.model.dto.ConfirmRefundResultDTO;
import com.cosfo.mall.payment.model.dto.PaymentDTO;
import com.cosfo.mall.payment.service.PaymentService;
import com.cosfo.mall.payment.strategy.PayStrategy;
import com.cosfo.mall.facade.dto.payment.PaymentChannelQueryByIdDTO;
import com.cosfo.mall.facade.payment.PaymentChannelFacade;
import com.cosfo.mall.common.utils.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.payment.trade.model.request.UnifiedProfitSharingRefundRequest;
import net.summerfarm.payment.trade.model.response.UnifiedProfitSharingRefundResult;
import net.summerfarm.payment.trade.model.common.SplitRule;
import net.summerfarm.payment.trade.service.PaymentClientService;
import net.summerfarm.payment.trade.model.config.ChannelConfig;
import net.xianmu.common.constant.NumberConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 智付分账回退策略实现
 *
 * @author: AI Assistant
 * @date: 2025-01-04
 */
@Service
@Slf4j
public class DinPayStrategy implements PayStrategy {

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private PaymentClientService paymentClientService;

    @Autowired
    private PaymentChannelFacade paymentChannelFacade;

    @Override
    public ConfirmRefundResultDTO confirmRefund(List<BillProfitSharingDTO> billProfitSharingList, Long tenantId) {
        try {
            log.info("开始智付分账回退处理 - 租户ID：{}，分账回退明细数量：{}", tenantId, billProfitSharingList.size());

            // 1. 构建智付分账回退请求
            UnifiedProfitSharingRefundRequest request = buildDinPayProfitSharingRefundRequest(billProfitSharingList, tenantId);

            // 2. 调用智付分账回退接口
            UnifiedProfitSharingRefundResult result = paymentClientService.refundProfitSharing(request);

            // 3. 处理分账回退结果并转换为通用格式
            return handleDinPayProfitSharingRefundResult(result, request);

        } catch (Exception e) {
            log.error("智付分账回退失败，租户ID：{}，分账回退明细数量：{}", tenantId, billProfitSharingList.size(), e);
            // 返回失败结果
            return ConfirmRefundResultDTO.fromDinPay(null, "FAILED", "ERROR", e.getMessage());
        }
    }
