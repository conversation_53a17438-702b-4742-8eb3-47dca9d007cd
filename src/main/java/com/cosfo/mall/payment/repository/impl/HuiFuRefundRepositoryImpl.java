package com.cosfo.mall.payment.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.mall.payment.mapper.HuiFuRefundMapper;
import com.cosfo.mall.payment.model.po.HuiFuRefund;
import com.cosfo.mall.payment.repository.HuiFuRefundRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 汇付退款服务层
 * @author: George
 * @date: 2023-12-13
 **/
@Service
public class HuiFuRefundRepositoryImpl extends ServiceImpl<HuiFuRefundMapper, HuiFuRefund> implements HuiFuRefundRepository {
    @Resource
    private HuiFuRefundMapper huiFuRefundMapper;

    @Override
    public HuiFuRefund queryLastByRefundId(Long refundId) {
        LambdaQueryWrapper<HuiFuRefund> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HuiFuRefund::getRefundId, refundId).orderByDesc(HuiFuRefund::getId);
        queryWrapper.last(" limit 1");
        return huiFuRefundMapper.selectOne(queryWrapper);
    }
}
