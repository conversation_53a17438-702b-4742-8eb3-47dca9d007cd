package com.cosfo.mall.payment.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.shade.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cosfo.mall.bill.model.dto.BillProfitSharingDTO;
import com.cosfo.mall.bill.service.BillProfitSharingService;
import com.cosfo.mall.common.config.TenantGrayConfig;
import com.cosfo.mall.common.constant.Constants;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.*;
import com.cosfo.mall.common.context.PaymentEnums;
import com.cosfo.mall.common.context.RefundEnum;
import com.cosfo.mall.common.factory.PayStrategyFactory;
import com.cosfo.mall.common.factory.RefundStrategyFactory;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.common.task.RefundRetryTask;
import com.cosfo.mall.common.utils.SignatureUtil;
import com.cosfo.mall.common.utils.StringUtils;
import com.cosfo.mall.facade.MessageServiceFacade;
import com.cosfo.mall.facade.SendLogFacade;
import com.cosfo.mall.facade.usercenter.UserCenterTenantFacade;
import com.cosfo.mall.order.model.dto.AcctSplitBunchExtDTO;
import com.cosfo.mall.order.model.dto.HuiFuConfirmRefundResultDTO;
import com.cosfo.mall.order.model.dto.HuiFuPaymentRefundResponseDTO;
import com.cosfo.mall.payment.model.dto.ConfirmRefundResultDTO;
import com.cosfo.mall.payment.model.dto.PaymentDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.notify.DinRefundNotifyDTO;
import net.summerfarm.payment.trade.common.enums.DinPaymentEnum;
import com.cosfo.mall.common.constants.DinPayRefundStatusEnum;
import com.cosfo.mall.order.service.OrderAfterSaleService;
import com.cosfo.mall.payment.mapper.HuiFuRefundMapper;
import com.cosfo.mall.payment.mapper.RefundMapper;
import com.cosfo.mall.payment.model.dto.RefundAcctSplitDetailDTO;
import com.cosfo.mall.payment.model.dto.RefundDTO;
import com.cosfo.mall.payment.model.po.HuiFuRefund;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.PaymentCombinedDetail;
import com.cosfo.mall.payment.model.po.Refund;
import com.cosfo.mall.payment.model.request.RefundRequest;
import com.cosfo.mall.payment.service.PaymentCombinedDetailService;
import com.cosfo.mall.payment.service.PaymentService;
import com.cosfo.mall.payment.service.RefundService;
import com.cosfo.mall.tenant.model.po.Tenant;
import com.cosfo.mall.tenant.service.TenantAuthConnectionService;
import com.cosfo.mall.tenant.service.TenantService;
import com.cosfo.mall.wechat.bean.refund.RefundNotify;
import com.cosfo.message.client.enums.KeyCodeEnum;
import com.cosfo.message.client.enums.MessageContentTypeEnum;
import com.cosfo.message.client.req.NotifyMessageReq;
import com.cosfo.message.client.req.NotifyTipBodyReq;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/25  11:48
 */
@Slf4j
@Service
public class RefundServiceImpl implements RefundService {
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private HuiFuRefundMapper huiFuRefundMapper;
    @Resource
    private TenantAuthConnectionService tenantAuthConnectionService;
    @Lazy
    @Resource
    private PayStrategyFactory payStrategyFactory;
    @Resource
    private TenantGrayConfig tenantGrayConfig;
    @Lazy
    @Resource
    private RefundService refundService;
    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;
    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @Resource
    private SendLogFacade sendLogFacade;
    @Resource
    private BillProfitSharingService billProfitSharingService;
    @Resource
    private MessageServiceFacade messageServiceFacade;
    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;
    @Resource
    @Lazy
    private RefundStrategyFactory refundStrategyFactory;
    @Lazy
    @Resource
    private OrderAfterSaleService orderAfterSaleService;
    @Resource
    private RefundRetryTask refundRetryTask;
    @Resource
    private TenantService tenantService;
    @Resource
    private PaymentService paymentService;
    @Resource
    private PaymentCombinedDetailService paymentCombinedDetailService;

    @Override
    public ResultDTO refundRequest(RefundDTO refundDTO) {
        log.info("refund-request, dto = {}", JSON.toJSONString(refundDTO));

        RefundRequest request = new RefundRequest();
        request.setTenantId(refundDTO.getTenantId());
        request.setOrderId(refundDTO.getOrderId());
        request.setOrderAfterSaleId(refundDTO.getAfterSaleId());
        request.setRefundPrice(refundDTO.getRefundPrice());

        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(refundDTO.getOrderId()));
        Integer payType = orderDTO.getPayType();
        Integer onlinePayChannel = orderDTO.getOnlinePayChannel();
        if (Objects.equals(payType, com.cosfo.ordercenter.client.common.PayTypeEnum.COMBINED_PAY.getCode())) {
            PaymentCombinedDetail paymentCombinedDetail = paymentCombinedDetailService.querySuccessNativeCombinedDetailByOrderId(orderDTO.getTenantId(), orderDTO.getId());
            payType = TradeTypeEnum.getPayTypeByTradeType(paymentCombinedDetail.getTradeType());
            onlinePayChannel = null;
        }
        // 获取退款码
        String refundCode = PayCodeEnum.getRefundCode(payType, onlinePayChannel, refundDTO.getRefundPrice());
        refundStrategyFactory.getRefundChannel(refundCode).generateRefundInfo(request);
        return ResultDTO.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleWxRefundNotify(RefundNotify refundNotify) {
        log.info("处理退款数据，result：{}", JSONObject.toJSONString(refundNotify));
        Refund refund = refundMapper.selectByRefundId(refundNotify.getRefundId());
        if (!Objects.equals(refund.getRefundStatus(), RefundEnum.Status.IN_REFUND.getStatus())) {
            log.info("非处理中退款，跳过处理，refundId：{}", refundNotify.getRefundId());
            return;
        }
        // 幂等控制
        if (Objects.nonNull(refund)) {
            refund = refundMapper.selectByIdForUpdate(refund.getId());
            if (!Objects.equals(refund.getRefundStatus(), RefundEnum.Status.IN_REFUND.getStatus())) {
                log.info("非处理中退款，跳过处理，refundId：{}", refundNotify.getRefundId());
                return;
            }
        }

        Refund updateRefund = new Refund();
        updateRefund.setRefundId(refundNotify.getRefundId());
        updateRefund.setRefundNo(refundNotify.getOutRefundNo());
        updateRefund.setUserReceivedAccount(refundNotify.getUserReceivedAccount());
        updateRefund.setStatus(refundNotify.getRefundStatus());
        // 查询退款单
        if (RefundEnum.WxNotifyStatus.SUCCESS.getWxCode().equals(refundNotify.getRefundStatus())) {
            updateRefund.setRefundStatus(RefundEnum.Status.SUCCESS.getStatus());
            updateRefund.setSuccessTime(refundNotify.getSuccessTime());

            // 更新售后单状态
            orderAfterSaleService.payRefundSuccessDeal(refund.getAfterSaleId());
        } else {
            log.error(refund.getRefundNo() + "售后单退款失败");
            updateRefund.setRefundStatus(RefundEnum.Status.FAIL.getStatus());
        }
        refundMapper.updateByRefundNoSelective(updateRefund);
        // 若退款成功,发送通知
        sendSuccessNotifyMessage(refund, RefundEnum.WxNotifyStatus.SUCCESS.getWxCode().equals(refundNotify.getRefundStatus()));
    }

    /**
     * 异步添加帆台消息记录
     */
    public void sendSuccessNotifyMessage(Refund refund, boolean success) {
        if (!success) {
            return;
        }
        try {
//            OrderAfterSaleResultDTO orderAfterSale = orderAfterSaleServiceFacade.selectByPrimaryKey(refund.getAfterSaleId());
            List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryByIds(Lists.newArrayList(refund.getAfterSaleId())));
            if (CollectionUtils.isEmpty(afterSaleDTOList)) {
                return;
            }
            OrderAfterSaleResp afterSaleDTO = afterSaleDTOList.get(0);
            String afterSaleOrderNo = afterSaleDTO.getAfterSaleOrderNo();
            Long orderId = afterSaleDTO.getOrderId();
            String uniqueId = MessageRefundStatusEnum.SUCCESS.getUnique(afterSaleOrderNo);
            String refundPrice = org.apache.commons.lang3.StringUtils.EMPTY;
            if (Objects.nonNull(refund.getRefundPrice())) {
                refundPrice = new DecimalFormat("0.00").format(refund.getRefundPrice());
            }

            NotifyMessageReq notifyMessageReq = new NotifyMessageReq();
            notifyMessageReq.setTenantId(refund.getTenantId());
            notifyMessageReq.setPageId(orderId);
            notifyMessageReq.setUniqueId(uniqueId);
            notifyMessageReq.setTitle(MessageRefundStatusEnum.SUCCESS.getTitle());
            notifyMessageReq.setSubTitle(MessageRefundStatusEnum.SUCCESS.getSubTitle());
            notifyMessageReq.setMessageContentTypeEnum(MessageContentTypeEnum.AFTER_TENANT_NOTIFY);

            // 组装data
            List<NotifyTipBodyReq> list = Lists.newArrayList();
            NotifyTipBodyReq notifyTipBodyReq = new NotifyTipBodyReq();
            notifyTipBodyReq.setKeyCode(KeyCodeEnum.AFTER_SALE_NO.getCode());
            notifyTipBodyReq.setKeyValue(Lists.newArrayList(afterSaleOrderNo));
            list.add(notifyTipBodyReq);
            notifyTipBodyReq = new NotifyTipBodyReq();
            notifyTipBodyReq.setKeyCode(KeyCodeEnum.REFUND_PRICE.getCode());
            notifyTipBodyReq.setKeyValue(Lists.newArrayList(refundPrice));
            list.add(notifyTipBodyReq);
            notifyMessageReq.setDetailList(list);
            sendLogFacade.createNotifyMessage(notifyMessageReq);
        } catch (Exception e) {
            // 若出现异常,只输出告警日志,其他业务正常流转
            log.error("创建退款成功消息失败 success:{},refund:{}", success, JSON.toJSONString(refund), e);
        }
    }

    @Override
    public String handleHuiFuRefundNotify(HttpServletRequest request) {
        // 验签请参data
        String data = request.getParameter("resp_data");
        try {
            HuiFuPaymentRefundResponseDTO refundResponse = JSONObject.parseObject(data, HuiFuPaymentRefundResponseDTO.class);
            String huifuId = refundResponse.getHuifu_id();
            String publicKey = tenantAuthConnectionService.selectPublicKey(huifuId);
            // 验签请参sign
            String sign = request.getParameter("sign");
            log.info("汇付退款回调数据 sign：{}，data：{}", sign, data);
            // 使用汇付公钥验签
            if (!SignatureUtil.verify(data, publicKey, sign)) {
                throw new BizException("签名验证失败");
            }
            String respCode = refundResponse.getResp_code();
            String subRespCode = refundResponse.getSub_resp_code();
            if ("00000000".equals(subRespCode) || "00000000".equals(respCode)) {
                return refundService.huiFuRefundNotify(refundResponse);
            } else {
                // 业务处理失败
                log.warn("汇付退款回调返回状态码异常:{}", JSONObject.toJSONString(refundResponse));
                return org.apache.commons.lang3.StringUtils.EMPTY;
            }
        } catch (Exception e) {
            throw new BizException("异步回调处理失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String huiFuRefundNotify(HuiFuPaymentRefundResponseDTO refundResponse) {
        HuiFuRefund oldHuifuRefund = huiFuRefundMapper.selectOne(new LambdaQueryWrapper<HuiFuRefund>().eq(HuiFuRefund::getReqSeqId, refundResponse.getReq_seq_id()).orderByDesc(HuiFuRefund::getId).last(" limit 1"));
        Refund refund = refundMapper.selectByPrimaryKey(oldHuifuRefund.getRefundId());
        if (!Objects.equals(refund.getRefundStatus(), RefundEnum.Status.IN_REFUND.getStatus())) {
            log.info("非处理中退款，跳过处理，refundId：{}", refund.getId());
            return "";
        }
        // 幂等控制
        refund = refundMapper.selectByIdForUpdate(refund.getId());
        if (!Objects.equals(refund.getRefundStatus(), RefundEnum.Status.IN_REFUND.getStatus())) {
            log.info("非处理中退款，跳过处理，refundId：{}", refund.getRefundId());
            return "";
        }

        Refund updateRefund = new Refund();
        updateRefund.setRefundNo(refund.getRefundNo());
        updateRefund.setStatus(refundResponse.getTrans_stat());
        BigDecimal feeAmount = BigDecimal.ZERO;
        // 查询退款单
        if ("S".equals(refundResponse.getTrans_stat())) {
            updateRefund.setRefundStatus(RefundEnum.Status.SUCCESS.getStatus());
            updateRefund.setSuccessTime(LocalDateTime.now());

            // 更新售后单状态
            Long afterSaleId = refund.getAfterSaleId();
            orderAfterSaleService.payRefundSuccessDeal(afterSaleId);

            if (refund.getAfterSaleId() != null) {
                //插入收支明细信息
                if (ObjectUtil.isNotNull(refundResponse.getAcct_split_bunch())) {
                    log.info("refundResponse.getAcct_split_bunch():JSON{},normal:{}", JSONObject.toJSONString(refundResponse.getAcct_split_bunch()), refundResponse.getAcct_split_bunch());
                    AcctSplitBunchExtDTO acctSplitBunchDTO = JSONObject.parseObject(refundResponse.getAcct_split_bunch(), AcctSplitBunchExtDTO.class);
                    // 接收的退款返回参数手续费不为空
                    log.info("acctSplitBunchDTO.getFee_amt()：{}", acctSplitBunchDTO.getFee_amt());
                    if (ObjectUtil.isNotNull(acctSplitBunchDTO.getFee_amt())) {
//                        bill.setFeeAmount(new BigDecimal(acctSplitBunchDTO.getFee_amt()));
                        feeAmount = new BigDecimal(acctSplitBunchDTO.getFee_amt());
                    }
                } else if (org.apache.commons.lang3.StringUtils.isNotEmpty(refundResponse.getFee_amt())) {
                    // 主动查询结果会返回具体手续费
//                    bill.setFeeAmount(new BigDecimal(refundResponse.getFee_amt()));
                    feeAmount = new BigDecimal(refundResponse.getFee_amt());
                } else {
                    log.info("汇付回调退款单不存在手续费.refund{}:refundResponse{}", JSONObject.toJSONString(refund), refundResponse);
                }
            }
        } else if ("F".equals(refundResponse.getTrans_stat())) {
            log.error(refund.getRefundNo() + "售后单退款失败");
            updateRefund.setRefundStatus(RefundEnum.Status.FAIL.getStatus());
        }
        refundMapper.updateByRefundNoSelective(updateRefund);

        // 更新汇付退款信息
        HuiFuRefund huiFuRefund = new HuiFuRefund();
        huiFuRefund.setHuifuId(refundResponse.getHuifu_id());
        if (!StringUtils.isBlank(refundResponse.getResp_code())) {
            huiFuRefund.setRespCode(refundResponse.getResp_code());
            huiFuRefund.setRespDesc(refundResponse.getResp_desc());
        } else {
            huiFuRefund.setRespCode(refundResponse.getSub_resp_code());
            huiFuRefund.setRespDesc(refundResponse.getSub_resp_desc());
        }
        huiFuRefund.setTransStat(refundResponse.getTrans_stat());
        huiFuRefund.setHfSeqId(refundResponse.getHf_seq_id());
        huiFuRefund.setReqDate(refundResponse.getReq_date());
        huiFuRefund.setReqSeqId(refundResponse.getReq_seq_id());
        huiFuRefund.setLoanFlag(refundResponse.getLoan_flag());
        huiFuRefund.setLoanUndertaker(refundResponse.getLoan_undertaker());
        huiFuRefund.setLoanAcctType(refundResponse.getLoan_acct_type());
        huiFuRefund.setUnconfirmAmt(refundResponse.getUnconfirm_amt());
        huiFuRefund.setConfirmedAmt(refundResponse.getConfirmed_amt());
        huiFuRefund.setFeeAmount(feeAmount.toPlainString());
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(refundResponse.getOrg_req_date())) {
            huiFuRefund.setOrgReqDate(refundResponse.getOrg_req_date());
        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(refundResponse.getOrg_hf_seq_id())) {
            huiFuRefund.setOrgReqSeqId(refundResponse.getOrg_hf_seq_id());
        }
        if (tenantGrayConfig.enableHuiFuRefundRetry(refund.getTenantId()) && Objects.nonNull(refundResponse.getReq_seq_id())) {
            huiFuRefundMapper.update(huiFuRefund, new LambdaUpdateWrapper<HuiFuRefund>().eq(HuiFuRefund::getRefundId, refund.getId()).eq(HuiFuRefund::getReqSeqId, refundResponse.getReq_seq_id()));
        } else {
            huiFuRefundMapper.update(huiFuRefund, new LambdaUpdateWrapper<HuiFuRefund>().eq(HuiFuRefund::getRefundId, refund.getId()));
        }
        // 若退款成功,发送通知
        sendSuccessNotifyMessage(refund, "S".equals(refundResponse.getTrans_stat()));
        return "RECV_ORD_ID_" + refundResponse.getReq_seq_id();
    }

    @Override
    public void confirmRefund(RefundDTO refundDTO) {
        // 分账退款明细
        List<RefundAcctSplitDetailDTO> refundAcctSplitDetailDTOList = refundDTO.getRefundAcctSplitDetailDTOList();
        RefundEnum.Status status = RefundEnum.Status.getByStatus(refundDTO.getRefundStatus());
        switch (status) {
            case CONFIRM_REFUND:
                // 是否有分账退款明细
                if (!CollectionUtils.isEmpty(refundAcctSplitDetailDTOList)) {
//                    // 交易汇付商户账户金额是否足够
//                    List<RefundAcctSplitDetailDTO> refundAcctSplitDetailDTOS = checkAccountBalance(refundAcctSplitDetailDTOList);
//                    if (!CollectionUtils.isEmpty(refundAcctSplitDetailDTOS)) {
//                        int result = refundMapper.increaseRetryNumByIdAndStatus(refundDTO.getId(), RefundEnum.Status.CONFIRM_REFUND.getStatus());
//                        // 当次数满的时候，进行告警
//                        sendInsufficientAccountBalanceMessage(refundAcctSplitDetailDTOS, refundDTO);
//                        confirmRefundWarn(result, refundDTO.getRetryNum(), refundDTO.getMaxRetryNum());
//                        return;
//                    }

                    // 乐观更新为交易确认退款中状态
                    int result = refundMapper.updateStatusCas(refundDTO.getId(), RefundEnum.Status.CONFIRM_REFUND.getStatus(), RefundEnum.Status.IN_CONFIRM_REFUND.getStatus());
                    if (result <= 0) {
                        log.error("创建交易确认退款流水失败，售后单：" + refundDTO.getAfterSaleId());
                        return;
                    }

                    initiateConfirmRefund(refundDTO);
                }
                break;
            case IN_CONFIRM_REFUND:
                // 查询交易确认退款记录
                handleInConfirmRefundTask(refundDTO);
                break;
            default:
                throw new BizException("未知状态");
        }
    }

    /**
     * 处理在交易退款中任务
     *
     * @param refundDTO
     */
    private void handleInConfirmRefundTask(RefundDTO refundDTO) {
        if (Objects.isNull(refundDTO.getConfirmRefundReqId())) {
            throw new BizException("交易确认退款请求Id不存在");
        }

        List<BillProfitSharingDTO> billProfitSharingDTOS = billProfitSharingService.queryConfirmResultRecord(refundDTO.getTenantId(), refundDTO.getConfirmRefundReqId());
        if (CollectionUtils.isEmpty(billProfitSharingDTOS)) {
            throw new BizException("交易确认退款流水记录不存在");
        }

        Integer billProfitSharingStatus = billProfitSharingDTOS.get(0).getStatus();
        ProfitSharingResultEnum profitSharingResultEnum = ProfitSharingResultEnum.getByStatus(billProfitSharingStatus);
        switch (profitSharingResultEnum) {
            case PROCESSING:
                // 告警
                refundMapper.increaseRetryNumByIdAndStatus(refundDTO.getId(), RefundEnum.Status.IN_CONFIRM_REFUND.getStatus());
                throw new ProviderException("售后单：" + refundDTO.getAfterSaleId() + "交易确认退款返回状态为处理中，请联系三方人员处理");
            case FINISHED:
                int size = refundMapper.updateStatusCas(refundDTO.getId(), RefundEnum.Status.IN_CONFIRM_REFUND.getStatus(), RefundEnum.Status.CREATE_REFUND.getStatus());
                if (size <= NumberConstant.ZERO) {
                    throw new BizException("交易确认退款完成，退款任务状态更新为100失败");
                }

                break;
            case FAILED:
//                List<RefundAcctSplitDetailDTO> refundAcctSplitDetailDTOList = refundDTO.getRefundAcctSplitDetailDTOList();
//                // 交易汇付商户账户金额是否足够
//                List<RefundAcctSplitDetailDTO> refundAcctSplitDetailDTOS = checkAccountBalance(refundAcctSplitDetailDTOList);
//                if (!CollectionUtils.isEmpty(refundAcctSplitDetailDTOS)) {
//                    int result = refundMapper.increaseRetryNumByIdAndStatus(refundDTO.getId(), RefundEnum.Status.IN_CONFIRM_REFUND.getStatus());
//                    // 发送消息金额不足
//                    sendInsufficientAccountBalanceMessage(refundAcctSplitDetailDTOS, refundDTO);
//                    confirmRefundWarn(result, refundDTO.getRetryNum(), refundDTO.getMaxRetryNum());
//                    return;
//                }

                // 失败重新发起交易确认退款
                initiateConfirmRefund(refundDTO);
                break;
            default:
                throw new BizException("未知状态");
        }
    }

    /**
     * 发起确认退款
     *
     * @param refundDTO
     */
    private void initiateConfirmRefund(RefundDTO refundDTO) {
        // 先生成确认退款记录
        List<BillProfitSharingDTO> billProfitSharingDTOS = billProfitSharingService.createConfirmRefundRecord(refundDTO);

        // 根据支付渠道选择策略
        String payCode = getPayCodeByRefundDTO(refundDTO);
        log.info("分账回退处理 - 退款ID：{}，选择策略：{}", refundDTO.getId(), payCode);

        // 发起第三方交易确认退款
        ConfirmRefundResultDTO confirmRefundResultDTO = payStrategyFactory.getStrategy(payCode).confirmRefund(billProfitSharingDTOS, refundDTO.getTenantId());

        // 处理交易确认退款结果
        boolean updateStatusResult = billProfitSharingService.handleConfirmRefundRecordResult(confirmRefundResultDTO, billProfitSharingDTOS, refundDTO.getId());
        if (!updateStatusResult) {
            throw new BizException("更新退款任务状态失败");
        }

        // 处理分账回退结果状态
        handleConfirmRefundResultStatus(confirmRefundResultDTO, refundDTO);
    }

    /**
     * 根据退款信息获取支付策略代码
     */
    private String getPayCodeByRefundDTO(RefundDTO refundDTO) {
        // 查询支付信息获取渠道
        PaymentDTO paymentDTO = paymentService.querySuccessPaymentInfoByOrderId(refundDTO.getOrderId(), refundDTO.getTenantId());
        Integer onlinePayChannel = paymentDTO.getOnlinePayChannel();

        if (Objects.equals(onlinePayChannel, OnlinePayChannelEnum.HUIFU_PAY.getChannel())) {
            return PaymentEnums.HF_PAY.getPayCode();
        } else if (Objects.equals(onlinePayChannel, OnlinePayChannelEnum.DIN_PAY.getChannel())) {
            return PaymentEnums.DIN_PAY.getPayCode();
        } else {
            log.warn("不支持的分账回退渠道：{}，默认使用汇付", onlinePayChannel);
            return PaymentEnums.HF_PAY.getPayCode();
        }
    }

    /**
     * 处理分账回退结果状态
     */
    private void handleConfirmRefundResultStatus(ConfirmRefundResultDTO confirmRefundResultDTO, RefundDTO refundDTO) {
        String transStat = confirmRefundResultDTO.getTransStat();

        // 智付渠道的状态处理
        if (confirmRefundResultDTO.getRefundOrderNo() != null) {
            handleDinPayConfirmRefundStatus(confirmRefundResultDTO, refundDTO);
        } else {
            // 汇付渠道的状态处理
            handleHuiFuConfirmRefundStatus(confirmRefundResultDTO, refundDTO);
        }
    }

    /**
     * 处理汇付分账回退状态
     */
    private void handleHuiFuConfirmRefundStatus(ConfirmRefundResultDTO confirmRefundResultDTO, RefundDTO refundDTO) {
        HuiFuStatusEnums huiFuStatusEnums = HuiFuStatusEnums.getByCode(confirmRefundResultDTO.getTransStat());
        switch (huiFuStatusEnums) {
            case PROCESSING:
                // 告警
                refundMapper.increaseRetryNumByIdAndStatus(refundDTO.getId(), RefundEnum.Status.IN_CONFIRM_REFUND.getStatus());
                throw new ProviderException("售后单：" + refundDTO.getAfterSaleId() + "交易确认退款返回状态为处理中，请联系三方人员处理");
            case SUCCESS:
                break;
            case FAILED:
                throw new ProviderException(confirmRefundResultDTO.getRespDesc());
            default:
                throw new BizException("未知状态");
        }
    }

    /**
     * 处理智付分账回退状态
     */
    private void handleDinPayConfirmRefundStatus(ConfirmRefundResultDTO confirmRefundResultDTO, RefundDTO refundDTO) {
        String refundStatus = confirmRefundResultDTO.getRefundStatus();

        if ("SUCCESS".equals(refundStatus)) {
            // 成功，继续处理
            log.info("智付分账回退成功 - 退款ID：{}，分账回退单号：{}", refundDTO.getId(), confirmRefundResultDTO.getRefundOrderNo());
        } else if ("DOING".equals(refundStatus)) {
            // 处理中，需要告警
            refundMapper.increaseRetryNumByIdAndStatus(refundDTO.getId(), RefundEnum.Status.IN_CONFIRM_REFUND.getStatus());
            throw new ProviderException("售后单：" + refundDTO.getAfterSaleId() + "智付分账回退返回状态为处理中，请联系三方人员处理");
        } else if ("FAILED".equals(refundStatus)) {
            // 失败
            throw new ProviderException(confirmRefundResultDTO.getRespDesc());
        } else {
            // 未知状态
            log.warn("智付分账回退返回未知状态：{}", refundStatus);
            throw new BizException("智付分账回退返回未知状态：" + refundStatus);
        }

        // TODO: 实现智付分账回退查询逻辑
        // queryAndUpdateDinPayProfitSharingRefundStatus(...)
    }

//
//    /**
//     * 发送账户余额不足消息
//     *
//     * @param insufficientAccountBalance
//     */
//    private void sendInsufficientAccountBalanceMessage(List<RefundAcctSplitDetailDTO> insufficientAccountBalance, RefundDTO refundDTO) {
//        NotifyMessageReq notifyMessageReq = new NotifyMessageReq();
//        notifyMessageReq.setTenantId(refundDTO.getTenantId());
//        notifyMessageReq.setTitle("退款失败");
//        notifyMessageReq.setSubTitle("因退款账户资金不足，退款失败");
//        notifyMessageReq.setMessageContentTypeEnum(MessageContentTypeEnum.AFTER_TENANT_NOTIFY);
//        notifyMessageReq.setPageId(refundDTO.getOrderId());
//        List<OrderAfterSaleDTO> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryService.queryByIds(Lists.newArrayList(refundDTO.getAfterSaleId())));
//        OrderAfterSaleDTO afterSaleDTO = afterSaleDTOList.get(0);
////        OrderAfterSale orderAfterSale = afterSaleService.selectByPrimaryKey(refundDTO.getAfterSaleId());
//        List<NotifyTipBodyReq> list = Lists.newArrayList();
//        NotifyTipBodyReq notifyTipBodyReq = new NotifyTipBodyReq();
//        notifyTipBodyReq.setKeyCode(KeyCodeEnum.AFTER_SALE_NO.getCode());
//        notifyTipBodyReq.setKeyValue(Lists.newArrayList(afterSaleDTO.getAfterSaleOrderNo()));
//
//        list.add(notifyTipBodyReq);
//        notifyTipBodyReq = new NotifyTipBodyReq();
//        notifyTipBodyReq.setKeyCode(KeyCodeEnum.REFUND_PRICE.getCode());
//        notifyTipBodyReq.setKeyValue(Lists.newArrayList(String.format("%.2f", refundDTO.getRefundPrice())));
//
//        list.add(notifyTipBodyReq);
//        notifyTipBodyReq = new NotifyTipBodyReq();
//        notifyTipBodyReq.setKeyCode(KeyCodeEnum.UNDERFUNDED_ACCOUNT.getCode());
//
//        List<Long> tenantIds = insufficientAccountBalance.stream().map(RefundAcctSplitDetailDTO::getAcctSplitTenantId).collect(Collectors.toList());
//        List<TenantAndBusinessInfoResultResp> tenantAndBusinessInfoResultResps = userCenterTenantFacade.getTenantAndCompanyByIds(tenantIds);
//        Map<Long, TenantAndBusinessInfoResultResp> tenantMap = tenantAndBusinessInfoResultResps.stream().collect(Collectors.toMap(TenantAndBusinessInfoResultResp::getTenantId, item -> item));
//        List<String> keyValue = new ArrayList<>();
//        for (RefundAcctSplitDetailDTO refundAcctSplitDetailDTO : insufficientAccountBalance) {
//            TenantAndBusinessInfoResultResp tenantAndBusinessInfoResultResp = tenantMap.get(refundAcctSplitDetailDTO.getAcctSplitTenantId());
//            keyValue.add(tenantAndBusinessInfoResultResp.getCompanyName() + "-" + refundAcctSplitDetailDTO.getHuifuId());
//        }
//
//        notifyTipBodyReq.setKeyValue(keyValue);
//        list.add(notifyTipBodyReq);
//        notifyMessageReq.setDetailList(list);
//        String uniqueId = MessageRefundStatusEnum.FAIL.getUnique(afterSaleDTO.getAfterSaleOrderNo());
//        notifyMessageReq.setUniqueId(uniqueId);
//        messageServiceFacade.createNotifyMessage(notifyMessageReq);
//    }

    @Override
    public void executeRefund(Long refundId) {
        Refund refund = refundMapper.selectByPrimaryKey(refundId);
        refundRetryTask.executeRetry(Arrays.asList(refund), 10);
    }


    public int batchInsert(List<Refund> refunds) {
        return refundMapper.batchInsert(refunds);
    }


    @Override
    public List<Refund> querySuccessRefunds(Collection<Long> tenantIds, String startTime, String endTime) {
        return refundMapper.querySuccessRefundsByTenant(tenantIds, startTime, endTime);
    }

    @Override
    public List<Refund> queryAfterSaleIds(Long tenantId, List<Long> orderAfterSaleIds) {
        return refundMapper.queryAfterSaleIds(tenantId, orderAfterSaleIds);
    }

    @Override
    public void logRefundResult(Refund refund, Exception e, Integer warningNum) {
        String detailInfo = getRefundDetailInfo(refund, e);
        Integer retryNum = refund.getRetryNum();
        // 能整除告警次数则error告警
        if (e != null && retryNum % warningNum == 0) {
            log.error("退款定时任务执行情况,详细信息：{}", detailInfo, e);
            return;
        }
        if (e != null) {
            log.warn("退款定时任务执行情况,详细信息：{}", detailInfo, e);
            return;
        }
        log.info("退款定时任务执行情况,详细信息：{}", detailInfo);
    }

    private String getRefundDetailInfo(Refund refund, Exception e) {
        if (refund == null) {
            return Constants.EMPTY_STRING;
        }
        Map<String, Object> refundDetailInfo = new LinkedHashMap<>();

        // 租户信息
        Long tenantId = refund.getTenantId();
        Tenant tenant = tenantService.selectByPrimaryKey(tenantId);
        String afterSaleNo = getAfterSaleNo(refund.getAfterSaleId());
        // 支付单信息
        Payment payment = paymentService.queryByRefundId(refund.getId());
        // 组装信息
        String tenantInfo = tenant.getTenantName() + "(" + tenantId + ")";
        refundDetailInfo.put("租户名称", tenantInfo);
        refundDetailInfo.put("售后单号", afterSaleNo);
        refundDetailInfo.put("退款单号", refund.getRefundNo());
        refundDetailInfo.put("退款渠道", payment == null ? "" : TradeTypeEnum.getDetailDescByTradeType(payment.getTradeType()));
        refundDetailInfo.put("执行结果", e == null ? "成功" : "失败");
        refundDetailInfo.put("重试次数", refund.getRetryNum() + 1);
        if (e != null) {
            refundDetailInfo.put("异常信息", e.getMessage());
        }
        return refundDetailInfo.toString();
    }

    private String getAfterSaleNo(Long afterSaleId) {
        if (afterSaleId == null) {
            return Constants.EMPTY_STRING;
        }
        List<OrderAfterSaleResp> orderAfterSaleDTOS = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryByIds(Collections.singletonList(afterSaleId)));
        OrderAfterSaleResp orderAfterSaleDTO = orderAfterSaleDTOS.get(0);
        return orderAfterSaleDTO.getAfterSaleOrderNo();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String handleDinPayRefundNotify(DinRefundNotifyDTO refundNotifyDTO) {
        try {
            log.info("开始处理智付退款回调 - 退款单号：{}，状态：{}",
                    refundNotifyDTO.getRefundOrderNo(), refundNotifyDTO.getRefundOrderStatus());

            // 根据退款单号查询退款记录
            Refund refund = refundMapper.selectByRefundNo(refundNotifyDTO.getRefundOrderNo());
            if (refund == null) {
                log.error("智付退款回调：未找到退款记录 - 退款单号：{}", refundNotifyDTO.getRefundOrderNo());
                return DinPaymentEnum.responseCode.NOTIFY_PROCESS_FAIL.getCode();
            }

            // 检查退款状态，避免重复处理
            if (!RefundEnum.Status.IN_REFUND.getStatus().equals(refund.getRefundStatus())) {
                log.info("智付退款回调：退款状态非处理中，跳过处理 - 退款单ID：{}，当前状态：{}",
                        refund.getId(), refund.getRefundStatus());
                return DinPaymentEnum.responseCode.NOTIFY_PROCESS_SUCCESS.getCode();
            }

            // 根据智付退款状态更新本地退款状态
            boolean isSuccess = handleDinPayRefundStatus(refund, refundNotifyDTO);

            // 发送成功通知消息
            if (isSuccess) {
                sendSuccessNotifyMessage(refund, true);
            }

            log.info("智付退款回调处理完成 - 退款单ID：{}，处理结果：{}", refund.getId(), isSuccess);
            return DinPaymentEnum.responseCode.NOTIFY_PROCESS_SUCCESS.getCode();

        } catch (Exception e) {
            log.error("智付退款回调处理异常 - 退款单号：{}", refundNotifyDTO.getRefundOrderNo(), e);
            throw new BizException("智付退款回调处理失败", e);
        }
    }

    /**
     * 处理智付退款状态
     */
    private boolean handleDinPayRefundStatus(Refund refund, DinRefundNotifyDTO refundNotifyDTO) {
        String refundOrderStatus = refundNotifyDTO.getRefundOrderStatus();
        DinPayRefundStatusEnum statusEnum = DinPayRefundStatusEnum.getByCode(refundOrderStatus);

        log.info("智付退款状态处理 - 退款单ID：{}，状态：{}({})",
                refund.getId(), statusEnum.getDesc(), statusEnum.getCode());

        // 更新退款记录
        Refund updateRefund = new Refund();
        updateRefund.setId(refund.getId());

        boolean isSuccess = false;

        if (statusEnum.isSuccess()) {
            // 退款成功
            updateRefund.setRefundStatus(RefundEnum.Status.SUCCESS.getStatus());
            updateRefund.setSuccessTime(LocalDateTime.now());
            updateRefund.setFeeAmount(refundNotifyDTO.getRefundFee());
            isSuccess = true;

            // 更新售后单状态
            orderAfterSaleService.payRefundSuccessDeal(refund.getAfterSaleId());

            log.info("智付退款成功 - 退款单ID：{}，退款单号：{}", refund.getId(), refund.getRefundNo());

        } else if (statusEnum.isFailure()) {
            // 退款失败或关闭
            updateRefund.setRefundStatus(RefundEnum.Status.FAIL.getStatus());
            log.error("智付退款失败 - 退款单ID：{}，退款单号：{}，状态：{}",
                    refund.getId(), refund.getRefundNo(), statusEnum.getDesc());

        } else if (statusEnum.isProcessing()) {
            // 处理中状态，不更新退款状态
            log.info("智付退款处理中 - 退款单ID：{}，状态：{}", refund.getId(), statusEnum.getDesc());
            return false;

        } else {
            // 未知状态
            log.warn("智付退款状态未知 - 退款单ID：{}，状态：{}", refund.getId(), refundOrderStatus);
            return false;
        }

        // 更新退款记录
        int updateResult = refundMapper.updateByPrimaryKeySelective(updateRefund);
        if (updateResult <= 0) {
            log.error("更新智付退款状态失败 - 退款单ID：{}", refund.getId());
            throw new BizException("更新退款状态失败");
        }

        return isSuccess;
    }
}
