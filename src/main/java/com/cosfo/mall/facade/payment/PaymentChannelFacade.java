package com.cosfo.mall.facade.payment;

import com.cosfo.mall.facade.converter.PaymentChannelConvert;
import com.cosfo.mall.facade.dto.payment.PaymentChannelQueryByIdDTO;
import com.cosfo.mall.facade.dto.payment.PaymentRoutingDTO;
import com.cosfo.mall.facade.dto.payment.PaymentRoutingQueryDTO;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.payment.PaymentChannelProvider;
import net.summerfarm.client.provider.payment.PaymentRuleRoutingProvider;
import net.summerfarm.client.req.payment.PaymentRoutingQueryReq;
import net.summerfarm.client.resp.payment.PaymentChannelQueryByIdResp;
import net.summerfarm.client.resp.payment.PaymentRoutingQueryResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * @description:
 * @author: George
 * @date: 2025-08-21
 **/
@Service
@Slf4j
public class PaymentChannelFacade {

    @DubboReference
    private PaymentChannelProvider paymentChannelProvider;
    @DubboReference
    private PaymentRuleRoutingProvider paymentRuleRoutingProvider;

    /**
     * 根据渠道ID查询支付渠道详细信息
     *
     * @param channelId
     * @return
     */
    public PaymentChannelQueryByIdDTO queryPaymentChannelById(Long channelId) {
        DubboResponse<PaymentChannelQueryByIdResp> resp = paymentChannelProvider.queryById(channelId);
        if (!resp.isSuccess()) {
            throw new ProviderException(resp.getMsg());
        }
        return PaymentChannelConvert.convertToPaymentChannelQueryByIdDTO(resp.getData());
    }

    /**
     * 根据路由信息查询支付渠道
     *
     * @param dto
     * @return
     */
    public PaymentRoutingDTO getRoutingInfo(PaymentRoutingQueryDTO dto) {
        PaymentRoutingQueryReq req = PaymentChannelConvert.convertToPaymentRoutingQueryReq(dto);
        DubboResponse<PaymentRoutingQueryResp> resp = paymentRuleRoutingProvider.getRoutingInfo(req);
        if (!resp.isSuccess()) {
            throw new ProviderException(resp.getMsg());
        }
        return PaymentChannelConvert.convertToPaymentRoutingDTO(resp.getData());
    }


}
