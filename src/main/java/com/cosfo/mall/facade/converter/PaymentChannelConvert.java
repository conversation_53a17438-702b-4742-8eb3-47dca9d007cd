package com.cosfo.mall.facade.converter;

import com.cosfo.mall.facade.dto.payment.PaymentChannelQueryByIdDTO;
import com.cosfo.mall.facade.dto.payment.PaymentRoutingDTO;
import com.cosfo.mall.facade.dto.payment.PaymentRoutingQueryDTO;
import net.summerfarm.client.req.payment.PaymentRoutingQueryReq;
import net.summerfarm.client.resp.payment.PaymentChannelQueryByIdResp;
import net.summerfarm.client.resp.payment.PaymentRoutingQueryResp;

/**
 * @description:
 * @author: George
 * @date: 2025-08-21
 **/
public class PaymentChannelConvert {

    public static PaymentChannelQueryByIdDTO convertToPaymentChannelQueryByIdDTO(PaymentChannelQueryByIdResp resp) {
        PaymentChannelQueryByIdDTO paymentChannelQueryByIdDTO = new PaymentChannelQueryByIdDTO();
        paymentChannelQueryByIdDTO.setId(resp.getId());
        paymentChannelQueryByIdDTO.setTenantId(resp.getTenantId());
        paymentChannelQueryByIdDTO.setBusinessLine(resp.getBusinessLine());
        paymentChannelQueryByIdDTO.setChannelName(resp.getChannelName());
        paymentChannelQueryByIdDTO.setCompanyEntity(resp.getCompanyEntity());
        paymentChannelQueryByIdDTO.setMerchantNo(resp.getMerchantNo());
        paymentChannelQueryByIdDTO.setPublicKey(resp.getPublicKey());
        paymentChannelQueryByIdDTO.setPrivateKey(resp.getPrivateKey());
        paymentChannelQueryByIdDTO.setSecret(resp.getSecret());
        paymentChannelQueryByIdDTO.setUserId(resp.getUserId());
        paymentChannelQueryByIdDTO.setAppId(resp.getAppId());
        paymentChannelQueryByIdDTO.setAppSecret(resp.getAppSecret());
        paymentChannelQueryByIdDTO.setCertPath(resp.getCertPath());
        paymentChannelQueryByIdDTO.setAuthCode(resp.getAuthCode());
        paymentChannelQueryByIdDTO.setRefundPassword(resp.getRefundPassword());
        paymentChannelQueryByIdDTO.setOperatorAccount(resp.getOperatorAccount());
        return paymentChannelQueryByIdDTO;
    }

    public static PaymentRoutingQueryReq convertToPaymentRoutingQueryReq(PaymentRoutingQueryDTO req) {
        PaymentRoutingQueryReq paymentRoutingQueryReq = new PaymentRoutingQueryReq();
        paymentRoutingQueryReq.setTenantId(req.getTenantId());
        paymentRoutingQueryReq.setBusinessLine(req.getBusinessLine());
        paymentRoutingQueryReq.setPlatform(req.getPlatform());
        paymentRoutingQueryReq.setPaymentMethod(req.getPaymentMethod());
        paymentRoutingQueryReq.setRouteKey(req.getRouteKey());
        paymentRoutingQueryReq.setRouteValue(req.getRouteValue());
        return paymentRoutingQueryReq;
    }

    public static PaymentRoutingDTO convertToPaymentRoutingDTO(PaymentRoutingQueryResp data) {
        PaymentRoutingDTO paymentRoutingDTO = new PaymentRoutingDTO();
        paymentRoutingDTO.setChannelId(data.getChannelId());
        paymentRoutingDTO.setChannelCode(data.getChannelCode());
        paymentRoutingDTO.setTenantId(data.getTenantId());
        paymentRoutingDTO.setBusinessLine(data.getBusinessLine());
        paymentRoutingDTO.setMerchantNo(data.getMerchantNo());
        paymentRoutingDTO.setPublicKey(data.getPublicKey());
        paymentRoutingDTO.setPrivateKey(data.getPrivateKey());
        paymentRoutingDTO.setSecret(data.getSecret());
        paymentRoutingDTO.setCertPath(data.getCertPath());
        return paymentRoutingDTO;
    }
}
