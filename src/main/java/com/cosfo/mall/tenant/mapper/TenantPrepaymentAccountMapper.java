package com.cosfo.mall.tenant.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.mall.tenant.model.po.TenantPrepaymentAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 预付账户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Mapper
public interface TenantPrepaymentAccountMapper extends BaseMapper<TenantPrepaymentAccount> {

    /**
     * 扣减可用金额
     * @param id
     * @param changeAmount
     * @return
     */
    int decreaseAvailableAmount(@Param("id") Long id, @Param("changeAmount") BigDecimal changeAmount);

    /**
     * 增加可用金额
     * @param id
     * @param changeAmount
     * @return
     */
    int increaseAvailableAmount(@Param("id") Long id, @Param("changeAmount") BigDecimal changeAmount);

    /**
     * 查询品牌方给供应商预付的账户
     * @param tenantId 品牌方id
     * @param supplierTenantId 供应商id
     * @return
     */
    List<TenantPrepaymentAccount> queryTenant4SupplierPrepay(@Param("tenantId")Long tenantId, @Param("supplierTenantId") Long supplierTenantId);

    /**
     * 主键id查询(排他锁)
     * @param id
     * @return
     */
    TenantPrepaymentAccount selectByIdForUpdate(Long id);

    /**
     * 冻结可用金额
     *
     * @param id
     * @param changeAmount
     * @return
     */
    int freezeAvailableAmount(@Param("id") Long id, @Param("changeAmount") BigDecimal changeAmount);

    /**
     * 扣减冻结金额
     *
     * @param id
     * @param changeAmount
     * @return
     */
    int decreaseFreezeBalance(@Param("id") Long id, @Param("changeAmount") BigDecimal changeAmount);
}
