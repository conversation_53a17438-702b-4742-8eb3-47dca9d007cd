package com.cosfo.mall.market.home.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cofso.item.client.enums.ItemTypeEnum;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.ProductAgentSkuFeeRuleTypeEnum;
import com.cosfo.mall.common.result.PageResultDTO;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.facade.MarketFacade;
import com.cosfo.mall.facade.dto.DeliveryDate4SkuDTO;
import com.cosfo.mall.facade.dto.DeliveryDateSkuQueryDTO;
import com.cosfo.mall.facade.ofc.OfcDeliveryInfoFacade;
import com.cosfo.mall.market.home.service.HomeService;
import com.cosfo.mall.market.model.dto.MarketClassificationTreeDTO;
import com.cosfo.mall.market.model.dto.MarketItemDTO;
import com.cosfo.mall.market.model.query.MarketItemQuery;
import com.cosfo.mall.market.model.vo.CombineItemVO;
import com.cosfo.mall.market.model.vo.LadderPriceVO;
import com.cosfo.mall.market.service.MarketClassificationService;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import com.cosfo.mall.merchant.service.MerchantAddressService;
import com.cosfo.mall.order.mapper.TrolleyMapper;
import com.cosfo.mall.order.model.po.Trolley;
import com.cosfo.mall.product.mapper.ProductBrandMapper;
import com.cosfo.mall.product.model.dto.ProductAgentSkuFeeCountRuleDTO;
import com.cosfo.mall.product.model.dto.ProductAgentSkuFeeDTO;
import com.cosfo.mall.product.model.dto.ProductSkuDTO;
import com.cosfo.mall.product.model.po.ProductBrand;
import com.cosfo.mall.product.service.ProductAgentSkuFeeRuleService;
import com.cosfo.mall.product.service.ProductSkuService;
import com.cosfo.mall.stock.model.dto.StockDTO;
import com.cosfo.mall.stock.service.StockService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 商城小程序首页【分类】tab商品接口
 * @date 2022/5/13 9:16
 */
@Slf4j
@Service
public class HomeServiceImpl implements HomeService {

    @Resource
    private ProductBrandMapper productBrandMapper;
    @Resource
    private StockService stockService;
    @Resource
    private MerchantAddressService merchantAddressService;
    @Resource
    private TrolleyMapper trolleyMapper;
    @Resource
    private ProductAgentSkuFeeRuleService productAgentSkuFeeRuleService;
    @Resource
    private MarketFacade marketFacade;
    @Resource
    private MarketClassificationService marketClassificationService;
    @Resource
    private OfcDeliveryInfoFacade ofcDeliveryInfoFacade;
    @Resource
    private ProductSkuService productSkuService;

    @Override
    public ResultDTO<PageInfo<MarketItemDTO>> listAll(Integer pageIndex, Integer pageSize, MarketItemQuery itemQuery, LoginContextInfoDTO contextInfoDTO) {

        Long tenantId = contextInfoDTO.getTenantId();
        Long storeId = contextInfoDTO.getStoreId();
        MerchantAddressDTO addressDto = merchantAddressService.queryDefaultAddressDTO(storeId, tenantId);

        itemQuery.setTenantId(tenantId);
        itemQuery.setStoreId(storeId);
        itemQuery.setPageNum(pageIndex);
        itemQuery.setPageSize(pageSize);

        try {
            long startTime = System.currentTimeMillis();

            PageInfo<MarketItemDTO> itemDTOPageInfo = marketFacade.listAllMarketItemForHome(itemQuery);

            List<MarketItemDTO> itemDTOList = itemDTOPageInfo.getList();
            if (CollectionUtils.isEmpty(itemDTOList)) {
                return PageResultDTO.success();
            }

            // 库存处理， 排除组合包商品id
            List<Long> itemIds = itemDTOList.stream()
                .filter(e -> !GoodsTypeEnum.COMBINE_ITEM.getCode().equals(e.getGoodsType()))
                .map(MarketItemDTO::getId)
                .collect(Collectors.toList());

            long stockStartTime = System.currentTimeMillis();
            Map<Long, StockDTO> stockDtoMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(itemIds)) {
                stockDtoMap = stockService.queryStockAmount(contextInfoDTO, addressDto, itemIds, null);
            }

            for (MarketItemDTO itemDTO : itemDTOList) {
                if (!GoodsTypeEnum.COMBINE_ITEM.getCode().equals(itemDTO.getGoodsType())) {
                    StockDTO stockDTO = stockDtoMap.get(itemDTO.getId());
                    itemDTO.setAmount(Objects.isNull(stockDTO) ? NumberConstant.ZERO : stockDTO.getAmount());
                    itemDTO.setExpirationTime(Objects.isNull(stockDTO) ? "" : Objects.isNull(stockDTO.getQuantityDate()) ? "" : stockDTO.getQuantityDate().toString());
                } else {
                    fillCombineItemStock(itemDTO, contextInfoDTO, addressDto);
                }
                //因为阶梯价 重新设置price=最低价格
                List<LadderPriceVO> ladderPrices = itemDTO.getLadderPrices ();
                if(CollectionUtil.isNotEmpty (ladderPrices) && ladderPrices.size ()>1){
                    itemDTO.setPrice (ladderPrices.stream().map (LadderPriceVO::getPrice).distinct ().min (BigDecimal::compareTo).get ());
                }
            }

            long stockEndTime = System.currentTimeMillis();
            log.info("线程{}：req={}, 整个库存调用执行时间：{}ms", Thread.currentThread().getName(), itemQuery, (stockEndTime - stockStartTime));

            long endTime = System.currentTimeMillis();
            log.info("线程{}：req={}, home-list接口本次执行时间：{}ms", Thread.currentThread().getName(), itemQuery, (endTime - startTime));

            return PageResultDTO.success(itemDTOPageInfo);
        } catch (Exception e) {
            log.error("home-listAll接口，异常信息：{}, req={}, ", e.getMessage(), itemQuery, e);
            return PageResultDTO.fail("功能升级中。。。");
        }
    }

    @Override
    public ResultDTO<MarketItemDTO> selectDetail(LoginContextInfoDTO contextInfoDTO, Long itemId) {
        Long tenantId = contextInfoDTO.getTenantId();
        Long storeId = contextInfoDTO.getStoreId();
        Long accountId = contextInfoDTO.getAccountId();

        // 组合品已返回 subItemList
        MarketItemDTO itemDTO = marketFacade.getMarketItemDetail(tenantId, storeId, itemId);
        if (itemDTO == null) {
            return ResultDTO.fail(ResultDTOEnum.ITEM_ON_SALE_DOWN.getCode(), "未查询到该商品信息");
        }
        if (Objects.isNull(itemDTO.getBasePrice())) {
            return ResultDTO.fail(ResultDTOEnum.ITEM_ON_SALE_DOWN.getCode(), ResultDTOEnum.ITEM_ON_SALE_DOWN.getMessage());
        }

        // 填充品牌名称
        if (Objects.nonNull(itemDTO.getBrandId())) {
            ProductBrand brand = productBrandMapper.selectByPrimaryKey(itemDTO.getBrandId());
            itemDTO.setBrandName(Objects.isNull(brand) ? "" : brand.getName());
        }


        // 库存信息处理
        MerchantAddressDTO merchantAddressDTO = merchantAddressService.queryDefaultAddressDTO(storeId, tenantId);

        if (!GoodsTypeEnum.COMBINE_ITEM.getCode().equals(itemDTO.getGoodsType())) {
            Map<Long, StockDTO> stockDTOMap = stockService.queryStockAmount(contextInfoDTO, merchantAddressDTO, Arrays.asList(itemId), null);
            StockDTO stockDTO = stockDTOMap.get(itemId);
            itemDTO.setAmount(Objects.isNull(stockDTO) ? NumberConstant.ZERO : stockDTO.getAmount());
            itemDTO.setExpirationTime(Objects.isNull(stockDTO) ? "" : Objects.isNull(stockDTO.getQuantityDate()) ? "" : stockDTO.getQuantityDate().toString());
        } else {
            fillCombineItemStock(itemDTO, contextInfoDTO, merchantAddressDTO);
        }
        fillCloseTimeAndDeliveryTime(itemDTO, tenantId, merchantAddressDTO);

        Trolley trolley = trolleyMapper.selectByItemId(tenantId, storeId, accountId, itemId);
        itemDTO.setTrolleyAmount(ObjectUtil.isNotNull(trolley) ? trolley.getAmount() : 0);

        // 处理大于起订量的规则
        if (GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(itemDTO.getGoodsType())) {
//            BigDecimal calPrice = marketItemService.calSelfSupplyMallPrice(tenantId, itemDTO.getPrice(), itemDTO.getMiniOrderQuantity(), null);
//            itemDTO.setPrice(calPrice);

            ProductAgentSkuFeeDTO productAgentSkuFeeDTO = productAgentSkuFeeRuleService.buildAgentSkuFeeRuleList(itemDTO.getTenantId(), itemDTO.getBasePrice());
            if (Objects.isNull(productAgentSkuFeeDTO) || !Objects.equals(productAgentSkuFeeDTO.getType(), ProductAgentSkuFeeRuleTypeEnum.ACCOUNT.getCode())) {
                itemDTO.setRuleList(new ArrayList<>());
            } else {
                Integer miniOrderQuantity = itemDTO.getMiniOrderQuantity();
                List<ProductAgentSkuFeeCountRuleDTO> countRuleDTOList = productAgentSkuFeeDTO.getCountRuleDTOList();
                List<ProductAgentSkuFeeCountRuleDTO> overMiniOrderQuantityRuleList = countRuleDTOList.stream().filter(el -> el.getCount() > miniOrderQuantity).collect(Collectors.toList());
                itemDTO.setRuleList(overMiniOrderQuantityRuleList);
            }
        }

        return ResultDTO.success(itemDTO);
    }
    // 查询结单时间 和 配送时间
    private void fillCloseTimeAndDeliveryTime(MarketItemDTO itemDTO, Long tenantId, MerchantAddressDTO merchantAddressDTO) {
        if (GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(itemDTO.getGoodsType())) {
            Long skuId = itemDTO.getSkuId ();
            String sku = null;
            if(ObjectUtil.isNotNull (skuId)){
                ProductSkuDTO productSkuDTO = productSkuService.queryById (skuId);
                if(ObjectUtil.isNotNull (productSkuDTO)){
                    sku = ObjectUtil.isNotNull (productSkuDTO.getSkuMapping ())?productSkuDTO.getSkuMapping ().getSku ():null;
                }
            }
            if(StringUtils.isEmpty (sku)){
                return;
            }

            DeliveryDateSkuQueryDTO deliveryDateSkuQueryDTO = new DeliveryDateSkuQueryDTO ();
            deliveryDateSkuQueryDTO.setTenantId(tenantId);
            deliveryDateSkuQueryDTO.setStoreId(merchantAddressDTO.getStoreId ());
            deliveryDateSkuQueryDTO.setCity(merchantAddressDTO.getCity ());
            deliveryDateSkuQueryDTO.setArea(merchantAddressDTO.getArea ());
            deliveryDateSkuQueryDTO.setSkuCodeList(Collections.singletonList (sku));

            List<DeliveryDate4SkuDTO> deliveryDate4SkuDTOS = ofcDeliveryInfoFacade.queryDeliveryDateBySku (deliveryDateSkuQueryDTO);
            if(CollectionUtils.isEmpty (deliveryDate4SkuDTOS)){
                return;
            }

            itemDTO.setDeliveryCloseTime (deliveryDate4SkuDTOS.get (0).getDeliveryCloseTime ());
            itemDTO.setDeliveryTime (deliveryDate4SkuDTOS.get (0).getDeliveryDate ());
            itemDTO.setFulfillmentType(deliveryDate4SkuDTOS.get (0).getFulfillmentType());
        }
    }

    private void fillCombineItemStock(MarketItemDTO itemDTO, LoginContextInfoDTO contextInfoDTO, MerchantAddressDTO merchantAddressDTO) {
        if (!ItemTypeEnum.COMBINE_ITEM.getCode().equals(itemDTO.getItemType())) {
            return;
        }
        List<Long> queryStockItemIdList = itemDTO.getSubItemList().stream().map(e -> e.getItemId()).collect(Collectors.toList());
        Map<Long, Integer> combineItemQuantityMap = itemDTO.getSubItemList().stream().collect(Collectors.toMap(CombineItemVO::getItemId, item -> item.getQuantity()));

        Map<Long, StockDTO> stockDTOMap = stockService.queryStockAmount(contextInfoDTO, merchantAddressDTO, queryStockItemIdList, true);

        // 组合包是否有库存
        boolean hasAmount = true;
        // 组合包子商品的库存 最小值
        Integer minAmountForSubItem = Integer.MAX_VALUE;
        for (Entry<Long, Integer> entry : combineItemQuantityMap.entrySet()) {
            StockDTO stockDTO = stockDTOMap.get(entry.getKey());
            Integer qty = entry.getValue();
            if (stockDTO == null || stockDTO.getAmount() == null) {
                // 如果任意一个子商品库存为空，组合包库存不足
                hasAmount = false;
                break;
            }
            if (stockDTO.getAmount().compareTo(qty) < 0) {
                // 如果任意一个子商品库存数量小于子商品数量，组合包库存不足
                hasAmount = false;
                break;
            }
            // 当前子商品在组合包下，可购买的组合包库存件数
            Integer enableQty = stockDTO.getAmount() / qty.intValue();
            minAmountForSubItem = Math.min(enableQty, minAmountForSubItem);
            if (stockDTO.getQuantityDate() != null) {
                itemDTO.setExpirationTime(stockDTO.getQuantityDate().toString());
            }
        }
        if (!hasAmount) {
            itemDTO.setAmount(NumberConstant.ZERO);
            itemDTO.setExpirationTime("");
        } else {
            itemDTO.setAmount(minAmountForSubItem);
        }
    }

    @Override
    public ResultDTO<List<MarketItemDTO>> unLoginListAll(Integer pageIndex, Integer pageSize, MarketItemQuery marketItemQuery) {
        boolean checkUnLoginClassification = checkUnLoginClassification(marketItemQuery.getTenantId(), marketItemQuery.getClassificationId());
        if (!checkUnLoginClassification) {
            throw new BizException("只可查看第一个分组下的商品！");
        }
        marketItemQuery.setPageNum(1);
        marketItemQuery.setPageSize(100);
        PageInfo<MarketItemDTO> itemDTOPageInfo = marketFacade.listAllMarketItemForHome(marketItemQuery);

        List<MarketItemDTO> marketItemDTOList = itemDTOPageInfo.getList();
        if (!CollectionUtils.isEmpty(marketItemDTOList)) {
            marketItemDTOList.stream().forEach(e -> {
                // 非null影响前端判断，所以置null
                e.setMiniOrderQuantity(null);
            });
            return ResultDTO.success(marketItemDTOList);
        }

        //String text = "[{\"amount\":1339,\"expirationTime\":\"2023-01-28\",\"id\":8336,\"mainPicture\":\"picture-path/hf2j4ecmu7iqcmmqh.jpeg\",\"skuId\":8336,\"specification\":\"1KG*12包\",\"specificationUnit\":\"箱\",\"title\":\"冷冻椰子水\",\"warehouseType\":1},{\"amount\":1339,\"expirationTime\":\"2023-01-28\",\"id\":8183,\"mainPicture\":\"picture-path/d10cpilap27j1px1e.jpeg\",\"skuId\":8183,\"specification\":\"毛重39-41斤/单果约200-300g\",\"specificationUnit\":\"一级\",\"title\":\"安徽翠冠梨\",\"warehouseType\":1},{\"amount\":1339,\"expirationTime\":\"2023-01-28\",\"id\":8147,\"mainPicture\":\"picture-path/g0ce1kvvuj6kv91ll.png\",\"skuId\":8147,\"specification\":\"850G\",\"specificationUnit\":\"罐\",\"title\":\"橙果粒罐头\",\"warehouseType\":1},{\"amount\":1339,\"expirationTime\":\"2023-01-28\",\"id\":8065,\"mainPicture\":\"picture-path/uv5xj0sizpapswu3.jpg\",\"skuId\":8065,\"specification\":\"1KG\",\"specificationUnit\":\"包\",\"title\":\"冷冻桑葚\",\"warehouseType\":1},{\"amount\":1339,\"expirationTime\":\"2023-01-28\",\"id\":8116,\"mainPicture\":\"picture-path/uck4te88yo7tcnro.jpeg\",\"skuId\":8116,\"specification\":\"毛重34-37斤/2粒\",\"specificationUnit\":\"一级\",\"title\":\"甜王西瓜\",\"warehouseType\":1}]";
        //if(itemDTO.getTenantId().equals(13L)){
        String text = "[{\n" +
            "\t\"amount\": 1339,\n" +
            "\t\"expirationTime\": \"2023-12-28\",\n" +
            "\t\"id\": 991,\n" +
            "\t\"mainPicture\": \"picture-path/15481442931540\",\n" +
            "\t\"skuId\": 813,\n" +
            "\t\"specification\": \"毛重4-4.5斤/一级/标准规格\",\n" +
            "\t\"specificationUnit\": \"箱\",\n" +
            "\t\"title\": \"红颜草莓\",\n" +
            "\t\"warehouseType\": 1\n" +
            "}]";
        //}

        List<MarketItemDTO> itemDTOList = JSONObject.parseArray(text, MarketItemDTO.class);
        return PageResultDTO.success(itemDTOList);
    }

    private boolean checkUnLoginClassification(Long tenantId, Long classificationId) {
        if (Objects.isNull(tenantId) || Objects.isNull(classificationId)) {
            throw new BizException("未登录下查询商品，租户ID与分类ID不可为空!");
        }
        try {
            ResultDTO<List<MarketClassificationTreeDTO>> resultDTO = marketClassificationService.noLoginSelectClassificationTree(tenantId);
            if (!resultDTO.isSuccess()) {
                log.error("查询未登录时可查看的分类信息失败！classificationResult:{}", JSONObject.toJSONString(resultDTO));
                return false;
            }
            List<MarketClassificationTreeDTO> marketClassificationTreeDTOList = resultDTO.getData();
            if (marketClassificationTreeDTOList.get(0).getChildList().get(0).getId().equals(classificationId)) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("查询未登录时可查看的分类信息失败,tenantId:{}", tenantId, e);
            return false;
        }
    }
}
