package com.cosfo.mall.payment;

import lombok.Data;
import net.summerfarm.payment.routing.model.dto.PaymentSceneConfigDTO;

import java.util.List;

/**
 * @description: 支付渠道保存参数对象
 * @author: George
 * @date: 2024-11-27
 **/
@Data
public class PaymentChannelSaveDTO {

    /**
     * id主键
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 渠道名称（微信原生、招行间连等）
     */
    private String channelName;

    /**
     * 企业主体
     */
    private String companyEntity;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 子商户号
     */
    private List<String> subMerchantNos;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * 密钥
     */
    private String secret;

    /**
     * 收银员Id
     */
    private String userId;

    /**
     * appId（招行）
     */
    private String appId;

    /**
     * appSecret（招行）
     */
    private String appSecret;

    /**
     * 证书路径（用于各渠道退款 微信原生v2版本需要该参数）
     */
    private String certPath;

    /**
     * 操作人ID
     */
    private Long operatorAdminId;

    /**
     * 对接码（火脸b2b）
     */
    private String authCode;

    /**
     * 退款密码（火脸b2b）
     */
    private String refundPassword;

    /**
     * 退款证书路径（火脸b2b）
     */
    private String operatorAccount;

    /**
     * SaaS业务线场景配置列表
     */
    private List<PaymentSceneConfigDTO> sceneConfigs;
}
