package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description: 支付渠道额外配置字段枚举类
 * @author: <PERSON>
 * @date: 2024-11-28
 **/
@Getter
@AllArgsConstructor
public enum PaymentChannelExtraConfigFiledEnums {

    CERT_PATH("certPath"),
    USERID("userId"),
    APPID("appId"),
    APP_SECRET("secret"),
    AUTH_CODE("authCode"),
    REFUND_PASSWORD("refundPassword"),
    OPERATOR_ACCOUNT("operatorAccount"),
    ;


    private String fieldName;
}
