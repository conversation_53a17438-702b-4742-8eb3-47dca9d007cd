package com.cosfo.mall.payment;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.Set;

/**
 * @description: 支付渠道状态枚举类
 * @author: <PERSON>
 * @date: 2024-11-28
 **/
@Getter
@AllArgsConstructor
public enum PaymentChannelStatusEnums {

    DISABLED(0, "禁用"),
    NORMAL(1, "正常"),
    DELETED(2, "删除"),
    ;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 描述
     */
    private String desc;

    /**
     * 获取存在的状态
     * @return
     */
    public static Set<Integer> getExistStatus() {
        return Sets.newHashSet(DISABLED.status, NORMAL.status);
    }

    /**
     * 获取非正常状态
     * @return
     */
    public static Set<Integer> getAbnormalStatus() {
        return Sets.newHashSet(DISABLED.status, DELETED.status);
    }

}
