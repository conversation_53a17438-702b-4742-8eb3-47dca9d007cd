package com.cosfo.mall.payment;

import net.summerfarm.payment.routing.model.domain.PaymentRuleRouting;
import net.summerfarm.payment.routing.model.dto.PaymentRuleRoutingQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
* @description: 支付规则路由DAO接口
* @author: George
* @date: 2024-11-26
**/
@Mapper
public interface PaymentRuleRoutingDAO {

    /**
     * 根据主键删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(PaymentRuleRouting record);

    /**
     * 选择性插入
     * @param record
     * @return
     */
    int insertSelective(PaymentRuleRouting record);

    /**
     * 根据主键查询
     * @param id
     * @return
     */
    PaymentRuleRouting selectByPrimaryKey(Long id);

    /**
     * 根据主键选择性更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(PaymentRuleRouting record);

    /**
     * 根据主键更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(PaymentRuleRouting record);

    /**
     * 查询支付规则路由
     * @param dto
     * @return
     */
    List<PaymentRuleRouting> listRuleRouting(PaymentRuleRoutingQueryDTO dto);

    /**
     * 根据支付规则ID查询
     * @param ruleIds
     * @return
     */
    List<PaymentRuleRouting> listByRuleIds(@Param("ruleIds") Collection<Long> ruleIds);


    /**
     * 根据支付规则ID和路由key查询
     * @param ruleIds
     * @return
     */
    List<PaymentRuleRouting> listByRuleIdsAndRouteKeys(@Param("ruleIds") Collection<Long> ruleIds, @Param("routeKeys") Collection<String> routeKeys);

    /**
     * 批量插入
     * @param routingList
     */
    void batchInsert(@Param("list") List<PaymentRuleRouting> routingList);

    /**
     * 根据支付规则ID删除
     * @param routingIds
     * @return
     */
    int deleteByIds(@Param("list") List<Long> routingIds);
}