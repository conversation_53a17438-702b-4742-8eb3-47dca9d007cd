package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2024-12-03
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentRoutingQueryDTO {

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 平台
     */
    private String platform;

    /**
     * 支付方式
     */
    private String paymentMethod;

    /**
     * 路由key
     */
    private String routeKey;

    /**
     * 路由值
     */
    private String routeValue;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 状态
     */
    private Integer status;

    /**
     * openId
     */
    private String openId;

    /**
     * mid
     */
    private Long mId;

    /**
     * 销售主体名称
     */
    private String sellingEntityName;

    /**
     * B2B路由开关状态(1开启，0关闭)
     */
    private Integer b2bSwitch;
}
