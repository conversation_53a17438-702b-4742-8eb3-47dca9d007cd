package com.cosfo.mall.payment;

import lombok.Data;

import java.util.List;

/**
 * @description: 支付渠道保存参数对象
 * @author: George
 * @date: 2024-11-27
 **/
@Data
public class PaymentChannelSaveParams {

    /**
     * 渠道名称（微信原生、招行间连、微信B2B支付等）
     */
    private String channelName;

    /**
     * 企业主体
     */
    private String companyEntity;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 子商户号
     */
    private List<String> subMerchantNos;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * 密钥
     */
    private String secret;

    /**
     * 收银员Id（招行）
     */
    private String userId;

    /**
     * appId（招行）
     */
    private String appId;

    /**
     * appSecret（招行）
     */
    private String appSecret;

    /**
     * 证书路径（用于各渠道退款 微信原生v2版本需要该参数）
     */
    private String certPath;

    /**
     * 对接码（火脸b2b）
     */
    private String authCode;

    /**
     * 退款密码（火脸b2b）
     */
    private String refundPassword;

    /**
     * 退款证书路径（火脸b2b）
     */
    private String operatorAccount;
}
