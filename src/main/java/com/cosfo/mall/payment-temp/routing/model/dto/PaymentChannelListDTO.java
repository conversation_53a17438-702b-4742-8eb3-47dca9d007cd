package com.cosfo.mall.payment;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @description: 支付渠道dto
 * @author: George
 * @date: 2024-11-27
 **/
@Data
public class PaymentChannelListDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 渠道名称（微信原生、招行间连等）
     */
    private String channelName;

    /**
     * 企业主体
     */
    private String companyEntity;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 操作人ID
     */
    private Long operatorAdminId;

    /**
     * 操作人名称
     */
    private String operatorAdminName;

    /**
     * 操作时间
     */
    private LocalDateTime operateTime;

    /**
     * 状态（0-禁用，1-启用， 2-已删除）
     */
    private Integer status;
}
