package com.cosfo.mall.payment;

import com.github.pagehelper.PageInfo;
import net.summerfarm.payment.routing.model.dto.*;
import net.summerfarm.payment.routing.model.dto.PaymentChannelCompanyEntityQueryDTO;
import net.summerfarm.payment.routing.model.dto.PaymentChannelDetailDTO;
import net.summerfarm.payment.routing.model.dto.PaymentChannelIdDTO;
import net.summerfarm.payment.routing.model.dto.PaymentChannelListDTO;
import net.summerfarm.payment.routing.model.dto.PaymentChannelQueryDTO;
import net.summerfarm.payment.routing.model.dto.PaymentChannelSaveDTO;
import net.summerfarm.payment.routing.model.dto.PaymentChannelStatusDTO;
import net.summerfarm.payment.routing.model.dto.PaymentChannelTiledDTO;

import java.util.List;

/**
 * @description: 支付渠道服务类
 * @author: George
 * @date: 2024-11-26
 **/
public interface PaymentChannelService {

    /**
     * 分页查询支付渠道
     * @param query
     * @return
     */
    PageInfo<net.summerfarm.payment.routing.model.dto.PaymentChannelListDTO> pageListChannel(net.summerfarm.payment.routing.model.dto.PaymentChannelQueryDTO query);

    /**
     * 查询支付渠道
     * @param query
     * @return
     */
    List<PaymentChannelListDTO> listChannel(PaymentChannelQueryDTO query);

    /**
     * 查询支付渠道详情
     * @param paymentChannelIdDTO
     * @return
     */
    PaymentChannelDetailDTO queryChannelDetail(PaymentChannelIdDTO paymentChannelIdDTO);

    /**
     * 保存支付渠道
     * @param paymentChannelSaveDTO
     * @return
     */
    Long saveChannel(net.summerfarm.payment.routing.model.dto.PaymentChannelSaveDTO paymentChannelSaveDTO);

    /**
     * 修改支付渠道状态
     * @param paymentChannelStatusDTO
     */
    boolean changeStatus(PaymentChannelStatusDTO paymentChannelStatusDTO);

    /**
     * 保存SaaS业务线支付渠道（包含渠道、规则、路由一体化配置）
     * @param paymentChannelSaveDTOList
     * @return
     */
    List<Long> saveChannel4SaaS(List<PaymentChannelSaveDTO> paymentChannelSaveDTOList);

    /**
     * 查询支付渠道公司实体
     * @param paymentChannelCompanyEntityQueryDTO
     * @return
     */
    List<String> queryCompanyEntities(PaymentChannelCompanyEntityQueryDTO paymentChannelCompanyEntityQueryDTO);

    /**
     * 根据ID查询支付渠道
     * @param id
     * @return
     */
    PaymentChannelTiledDTO selectById(Long id);

}
