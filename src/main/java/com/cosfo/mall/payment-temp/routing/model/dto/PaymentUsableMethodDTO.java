package com.cosfo.mall.payment;

import lombok.Builder;
import lombok.Data;
import lombok.Setter;

/**
 * @description: 支付可用方式ViewObject
 * @author: <PERSON>
 * @date: 2024-11-27
 **/
@Setter
@Data
@Builder
public class PaymentUsableMethodDTO {

    /**
     * 微信支付标识
     */
    private boolean wechatPayFlag;

    /**
     * 支付宝支付标识
     */
    private boolean alipayFlag;

    /**
     * 付款码支付标识
     */
    private boolean paymentCodeFlag;

    public boolean getWechatPayFlag() {
        return wechatPayFlag;
    }

    public boolean getAlipayFlag() {
        return alipayFlag;
    }

    public boolean getPaymentCodeFlag() {
        return paymentCodeFlag;
    }

}
