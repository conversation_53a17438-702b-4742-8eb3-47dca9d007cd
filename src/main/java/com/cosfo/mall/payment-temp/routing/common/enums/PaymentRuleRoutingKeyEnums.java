package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description: 路由规则枚举类
 * @author: <PERSON>
 * @date: 2024-11-29
 **/
@Getter
@AllArgsConstructor
public enum PaymentRuleRoutingKeyEnums {

    AREA_NO("areaNo", "运营区域"),

    B2B_AUTH_OPEN_ID("b2bAuthOpenId", "b2b门店助手授权openid"),

    MID("mid", "门店id"),

    TENANT_ID("tenantId", "租户ID")
    ;

    private String key;

    private String desc;
}
