package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description: 支付规则详情ViewObject
 * @author: George
 * @date: 2024-11-27
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PaymentRuleDetailDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 应用平台（mini app、h5等）
     */
    private String platform;

    /**
     * 支付方式（微信、支付宝、二维码等）
     */
    private String paymentMethod;

    /**
     * 渠道名称（微信原生、招行间连等）
     */
    private String channelName;

    /**
     * 企业主体
     */
    private String companyEntity;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 子商户号
     */
    private String subMerchantNo;

    /**
     * 运营服务区域
     */
    private List<Long> areaNos;

    /**
     * 渠道id
     */
    private Long channelId;

    /**
     * 门店ids
     */
    private List<Long> mids;
}
