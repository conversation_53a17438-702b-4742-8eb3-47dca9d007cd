package com.cosfo.mall.payment;

import com.github.pagehelper.PageInfo;
import net.summerfarm.payment.routing.model.dto.*;
import net.summerfarm.payment.routing.model.dto.PaymentRoutingQueryDTO;
import net.summerfarm.payment.routing.model.dto.PaymentRuleDTO;
import net.summerfarm.payment.routing.model.dto.PaymentRuleDetailDTO;
import net.summerfarm.payment.routing.model.dto.PaymentRuleListDTO;
import net.summerfarm.payment.routing.model.dto.PaymentRuleQueryDTO;
import net.summerfarm.payment.routing.model.dto.PaymentRuleRoutingUpdateDTO;
import net.summerfarm.payment.routing.model.dto.PaymentRuleSaveDTO;

/**
 * @description: 支付规则服务类
 * @author: George
 * @date: 2024-11-26
 **/
public interface PaymentRuleService {

        /**
        * 查询支付规则
        * @param query
        * @return
        */
        PageInfo<PaymentRuleListDTO> pageListRule(PaymentRuleQueryDTO query);

        /**
         * 查询支付规则详情
         * @param id
         * @return
         */
        PaymentRuleDetailDTO queryRuleDetail(Long id);

        /**
         * 保存支付规则
         * @param paymentRuleSaveDTO
         * @return
         */
        Long saveRule(PaymentRuleSaveDTO paymentRuleSaveDTO);

        /**
         * 更新支付路由
         * @param paymentRuleRoutingUpdateDTO
         */
        Boolean bindRuleRouting(PaymentRuleRoutingUpdateDTO paymentRuleRoutingUpdateDTO);

        /**
         * 查询最早的支付渠道规则
         * @param paymentRoutingQueryDTO
         * @return
         */
        PaymentRuleDTO selectEarliestRule(PaymentRoutingQueryDTO paymentRoutingQueryDTO);
}
