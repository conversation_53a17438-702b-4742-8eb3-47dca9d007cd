package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2024-11-28
 **/
@Getter
@AllArgsConstructor
public enum PaymentChannelProviderEnums {

    WECHAT_NATIVE(0,"微信原生"),

    CMB(4, "招行间连"),

    WECHAT_B2B(5, "微信B2B支付"),

    DIN_PAY(6, "智付间联"),

    HF(7, "汇付间联")
    ;

    private final Integer code;
    private final String channelName;

    public static Integer getCodeByName(String name) {
        for (PaymentChannelProviderEnums channelName : PaymentChannelProviderEnums.values()) {
            if (channelName.getChannelName().equals(name)) {
                return channelName.getCode();
            }
        }
        return null;
    }
}
