package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

/**
* @description: 支付账户信息
* @author: <PERSON>
* @date: 2024-12-10
**/
/**
 * 企业支付账号信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CompanyAccount {

    /**
     * 主键
     */
    private Integer id;

    /**
    * 企业名称
    */
    private String companyName;

    /**
    * 微信支付账号信息
    */
    private String wxAccountInfo;

    /**
    * 记录人id
    */
    private Integer adminId;

    /**
    * 添加时间
    */
    private LocalDateTime addtime;

    /**
    * 账号渠道 0微信 1中银 2招行
    */
    private Integer channel;

    /**
    * 微信APPID
    */
    private String mchAppId;

    /**
    * 公众号APPID字段
    */
    private String mchxAppId;

}