package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @author: George
 * @date: 2024-11-29
 **/
@Data
@Builder
public class PaymentRuleRoutingQueryDTO {

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 路由key
     * @see net.summerfarm.payment.routing.common.enums.PaymentRuleRoutingKeyEnums
     */
    private String routeKey;

    /**
     * 路由值
     */
    private List<String> routeValue;

    /**
     * 应用平台（mini app、h5等）
     */
    private String platform;

    /**
     * 支付方式（微信、支付宝、二维码等）
     */
    private String paymentMethod;
}
