package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: SaaS支付场景配置DTO
 * @author: Augment Agent
 * @date: 2025-08-15
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentSceneConfigDTO {

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 应用平台（mini app、h5等）
     */
    private String platform;

    /**
     * 支付方式（微信、支付宝、二维码等）
     */
    private String paymentMethod;
}
