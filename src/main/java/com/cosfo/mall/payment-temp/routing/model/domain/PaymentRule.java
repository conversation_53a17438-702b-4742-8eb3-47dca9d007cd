package com.cosfo.mall.payment;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @description: 支付规则
 * @author: <PERSON>
 * @date: 2024-11-26
 **/
@Data
public class PaymentRule {
    /**
    * 主键ID
    */
    private Long id;

    /**
    * 租户ID
    */
    private Long tenantId;

    /**
    * 业务线（summerfarm、pop、saas）
    */
    private String businessLine;

    /**
    * 场景名称
    */
    private String sceneName;

    /**
    * 应用平台（mini app、h5等）
    */
    private String platform;

    /**
    * 支付方式（微信、支付宝、二维码等）
    */
    private String paymentMethod;

    /**
    * 渠道ID
    */
    private Long channelId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}