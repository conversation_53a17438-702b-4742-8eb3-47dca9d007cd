package com.cosfo.mall.payment;

import net.summerfarm.payment.routing.model.dto.*;
import net.summerfarm.payment.routing.model.dto.PaymentRoutingDTO;
import net.summerfarm.payment.routing.model.dto.PaymentRoutingQueryDTO;
import net.summerfarm.payment.routing.model.dto.PaymentRuleRoutingUnBindDTO;
import net.summerfarm.payment.routing.model.dto.PaymentUsableMethodDTO;

/**
 * @description: 支付规则路由服务类
 * @author: George
 * @date: 2024-11-26
 **/
public interface PaymentRuleRoutingService {

    /**
     * 查询用户可用支付方式
     * @param paymentRoutingQueryDTO
     * @return
     */
    PaymentUsableMethodDTO queryUsableMethods(net.summerfarm.payment.routing.model.dto.PaymentRoutingQueryDTO paymentRoutingQueryDTO);

    /**
     * 查询支付路由
     * @param paymentRoutingQueryDTO
     * @return
     */
    PaymentRoutingDTO getRoutingInfo(PaymentRoutingQueryDTO paymentRoutingQueryDTO);

    /**
     * b2b解绑支付规则路由
     * @param unBindDTO
     */
    void unbindB2bAuthOpenId(PaymentRuleRoutingUnBindDTO unBindDTO);
}
