package com.cosfo.mall.payment;

import net.summerfarm.payment.trade.model.request.UnifiedClosePaymentRequest;
import net.summerfarm.payment.trade.model.request.UnifiedPaymentRequest;
import net.summerfarm.payment.trade.model.request.UnifiedQueryPaymentRequest;
import net.summerfarm.payment.trade.model.request.UnifiedRefundRequest;
import net.summerfarm.payment.trade.model.request.UnifiedQueryRefundRequest;
import net.summerfarm.payment.trade.model.request.UnifiedProfitSharingRequest;
import net.summerfarm.payment.trade.model.request.UnifiedProfitSharingRefundRequest;
import net.summerfarm.payment.trade.model.request.UnifiedQueryProfitSharingRequest;
import net.summerfarm.payment.trade.model.request.UnifiedQueryProfitSharingRefundRequest;
import net.summerfarm.payment.trade.model.response.UnifiedClosePaymentResult;
import net.summerfarm.payment.trade.model.response.UnifiedPaymentResult;
import net.summerfarm.payment.trade.model.response.UnifiedQueryPaymentResult;
import net.summerfarm.payment.trade.model.response.UnifiedRefundResult;
import net.summerfarm.payment.trade.model.response.UnifiedQueryRefundResult;
import net.summerfarm.payment.trade.model.result.UnifiedProfitSharingResult;
import net.summerfarm.payment.trade.model.result.UnifiedProfitSharingRefundResult;
import net.summerfarm.payment.trade.model.result.UnifiedQueryProfitSharingResult;
import net.summerfarm.payment.trade.model.result.UnifiedQueryProfitSharingRefundResult;

/**
 * 统一支付客户端服务接口
 * 负责所有主动发起的交易操作
 */
public interface PaymentClientService {

    // ========== 支付相关方法 ==========

    UnifiedPaymentResult pay(UnifiedPaymentRequest request);

    UnifiedQueryPaymentResult queryPayment(UnifiedQueryPaymentRequest request);

    UnifiedClosePaymentResult closePayment(UnifiedClosePaymentRequest request);

    // ========== 退款相关方法 ==========

    UnifiedRefundResult refund(UnifiedRefundRequest request);

    UnifiedQueryRefundResult queryRefund(UnifiedQueryRefundRequest request);

    // ========== 分账相关方法 ==========

    /**
     * 执行分账操作
     *
     * @param request 分账请求
     * @return 分账结果
     */
    UnifiedProfitSharingResult profitSharing(UnifiedProfitSharingRequest request);

    /**
     * 查询分账状态
     *
     * @param request 分账查询请求
     * @return 分账查询结果
     */
    UnifiedQueryProfitSharingResult queryProfitSharing(UnifiedQueryProfitSharingRequest request);

    /**
     * 执行分账回退操作
     *
     * @param request 分账回退请求
     * @return 分账回退结果
     */
    UnifiedProfitSharingRefundResult refundProfitSharing(UnifiedProfitSharingRefundRequest request);

    /**
     * 查询分账回退状态
     *
     * @param request 分账回退查询请求
     * @return 分账回退查询结果
     */
    UnifiedQueryProfitSharingRefundResult queryProfitSharingRefund(UnifiedQueryProfitSharingRefundRequest request);
}