package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 分账回退状态枚举
 * 
 * <AUTHOR> Agent
 * @date 2025-08-27
 */
@Getter
@AllArgsConstructor
public enum ProfitSharingRefundStatus {

    /**
     * 初始状态
     */
    INIT("INIT", "初始状态"),

    /**
     * 分账回退处理中
     */
    DOING("DOING", "处理中"),

    /**
     * 分账回退成功
     */
    SUCCESS("SUCCESS", "成功"),

    /**
     * 分账回退失败
     */
    FAILED("FAILED", "失败"),

    /**
     * 未知状态
     */
    UNKNOWN("UNKNOWN", "未知状态");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据状态码获取枚举
     * 
     * @param code 状态码
     * @return ProfitSharingRefundStatus
     */
    public static ProfitSharingRefundStatus fromCode(String code) {
        for (ProfitSharingRefundStatus status : ProfitSharingRefundStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return UNKNOWN;
    }

    /**
     * 是否为成功状态
     * 
     * @return boolean
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }

    /**
     * 是否为失败状态
     * 
     * @return boolean
     */
    public boolean isFailed() {
        return this == FAILED;
    }

    /**
     * 是否为处理中状态
     * 
     * @return boolean
     */
    public boolean isPending() {
        return this == DOING;
    }
}
