package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 分账规则模型
 *
 * <AUTHOR> Agent
 * @date 2025-08-27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SplitRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户号（必填）
     * 分账或回退的商户号
     */
    private String merchantNo;

    /**
     * 金额（必填，单位：元）
     * 分账或回退的金额
     */
    private BigDecimal amount;

    /**
     * 描述（可选）
     * 对本次分账或回退的描述
     */
    private String description;
}
