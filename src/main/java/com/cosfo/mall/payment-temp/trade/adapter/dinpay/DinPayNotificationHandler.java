package com.cosfo.mall.payment;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.payment.trade.adapter.dinpay.DinPayConfig;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinResponseDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.notify.DinNotifyDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.notify.DinPayNotifyDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.notify.DinRefundNotifyDTO;
import net.summerfarm.payment.trade.common.crypto.CertUtils;
import net.summerfarm.payment.trade.common.crypto.SM2Utils;
import net.summerfarm.payment.trade.common.enums.ErrorCode;
import net.summerfarm.payment.trade.common.exception.PaymentException;
import net.summerfarm.payment.trade.model.config.ChannelConfig;
import net.summerfarm.payment.trade.model.common.UnifiedError;

import java.security.PublicKey;
import java.security.Security;
import java.util.Objects;

/**
 * 智付（DinPay）通知处理器
 * 负责解析和验签智付的异步通知
 */
@Slf4j
public class DinPayNotificationHandler {

    private static final String NOTIFY_PROCESS_SUCCESS_CODE = "0000"; // 智付通知处理成功返回码

    public DinPayNotificationHandler() {
        Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
    }

    /**
     * 验证并解析智付通知
     *
     * @param rawNotificationData 原始通知数据
     * @param config 智付配置
     * @return 解析后的通知对象（可能是支付通知或退款通知）
     * @throws PaymentException 如果验签失败或数据解析异常
     */
    public Object verifyAndParse(String rawNotificationData, ChannelConfig config) {
        if (rawNotificationData == null || rawNotificationData.isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "通知数据不能为空");
        }
        if (config == null) {
            throw new PaymentException(ErrorCode.CHANNEL_CONFIG_ERROR, "渠道配置不能为空");
        }
        net.summerfarm.payment.trade.adapter.dinpay.DinPayConfig dinPayConfig = JSON.parseObject(JSON.toJSONString(config.getExtraConfig()), net.summerfarm.payment.trade.adapter.dinpay.DinPayConfig.class);

        // 1. 初步解析外层DinNotifyDTO
        DinNotifyDTO dinNotifyDTO = JSON.parseObject(rawNotificationData, DinNotifyDTO.class);
        validateNotifyParams(dinNotifyDTO);

        // 2. 验签
        validateSign(dinPayConfig, dinNotifyDTO.getData(), dinNotifyDTO.getSign());

        // 3. 解析内层业务数据
        // 根据通知类型（支付或退款）进一步解析
        // 智付的通知类型通常在内层data中，或者需要根据接口名称判断
        // 这里我们尝试解析为支付通知或退款通知
        try {
            // 尝试解析为支付通知
            DinPayNotifyDTO payNotify = JSON.parseObject(dinNotifyDTO.getData(), DinPayNotifyDTO.class);
            if (payNotify != null && payNotify.getOrderNo() != null) {
                log.info("解析为智付支付通知: {}", payNotify.getOrderNo());
                return payNotify;
            }
        } catch (Exception e) {
            log.warn("无法解析为智付支付通知, 尝试解析为退款通知. Error: {}", e.getMessage());
        }

        try {
            // 尝试解析为退款通知
            DinRefundNotifyDTO refundNotify = JSON.parseObject(dinNotifyDTO.getData(), DinRefundNotifyDTO.class);
            if (refundNotify != null && refundNotify.getRefundOrderNo() != null) {
                log.info("解析为智付退款通知: {}", refundNotify.getRefundOrderNo());
                return refundNotify;
            }
        } catch (Exception e) {
            log.warn("无法解析为智付退款通知. Error: {}", e.getMessage());
        }

        throw new PaymentException(ErrorCode.INVALID_PARAMETER, "无法解析智付通知数据类型");
    }

    /**
     * 验签
     */
    private void validateSign(DinPayConfig dinPayConfig, String data, String sign) {
        String publicKeyBase64 = dinPayConfig.getPublicKey();
        PublicKey platformPublicKey = CertUtils.getPublicKeyByBase64(publicKeyBase64);

        boolean verify = SM2Utils.verify(platformPublicKey, data, sign);
        if (!verify) {
            throw new PaymentException(ErrorCode.SIGNATURE_ERROR, "智付回调验签失败");
        }
        log.info("智付回调验签成功");
    }

    /**
     * 校验回调参数
     */
    private void validateNotifyParams(DinNotifyDTO dinNotifyDTO) {
        if (dinNotifyDTO == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "智付回调参数为空");
        }
        if (dinNotifyDTO.getData() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "智付回调参数data为空");
        }
        if (dinNotifyDTO.getMerchantId() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "智付回调参数merchantId为空");
        }
        if (dinNotifyDTO.getSignatureMethod() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "智付回调参数signatureMethod为空");
        }
        if (dinNotifyDTO.getSign() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "智付回调参数sign为空");
        }
    }

    /**
     * 获取智付通知处理成功时应返回给渠道的响应字符串
     */
    public String getSuccessResponse() {
        return NOTIFY_PROCESS_SUCCESS_CODE;
    }

    /**
     * 获取智付通知处理失败时应返回给渠道的响应字符串
     */
    public String getFailureResponse() {
        return "FAIL"; // 智付文档中通常是FAIL
    }
}
