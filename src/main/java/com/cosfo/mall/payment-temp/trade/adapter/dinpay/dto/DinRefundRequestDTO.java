package com.cosfo.mall.payment;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.model.config.ChannelConfig;

import java.math.BigDecimal;

/**
 * @description: 智付退款请求DTO
 * @author: George
 * @date: 2025-02-17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DinRefundRequestDTO {

    /**
     * 接口名称
     */
    private String interfaceName;

    /**
     * 原交易支付单号
     */
    private String payOrderNo;

    /**
     * 退款单号
     */
    private String refundOrderNo;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 回调url
     */
    private String notifyUrl;

    /**
     * url
     */
    private String url;

    /**
     * 渠道配置信息（不参与JSON序列化）
     */
    @JSONField(serialize = false, deserialize = false)
    private ChannelConfig channelConfig;

}
