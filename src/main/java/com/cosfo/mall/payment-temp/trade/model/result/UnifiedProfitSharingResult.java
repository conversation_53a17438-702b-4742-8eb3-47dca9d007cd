package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.common.enums.ProfitSharingStatus;
import net.summerfarm.payment.trade.model.common.UnifiedError;

import java.io.Serializable;

/**
 * 统一分账响应模型
 *
 * <AUTHOR> Agent
 * @date 2025-08-27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedProfitSharingResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分账订单号
     */
    private String profitSharingNo;

    /**
     * 原支付订单号
     */
    private String paymentNo;

    /**
     * 分账状态
     */
    private ProfitSharingStatus status;

    /**
     * 渠道分账订单号（如果有）
     */
    private String channelProfitSharingId;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误信息（失败时）
     */
    private UnifiedError error;

    /**
     * 响应时间戳
     */
    private Long timestamp;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 创建成功的分账结果
     *
     * @param profitSharingNo 分账订单号
     * @param paymentNo 原支付订单号
     * @param status 分账状态
     * @param channelName 渠道名称
     * @return UnifiedProfitSharingResult
     */
    public static UnifiedProfitSharingResult success(String profitSharingNo, String paymentNo,
                                                   ProfitSharingStatus status, String channelName) {
        return UnifiedProfitSharingResult.builder()
                .profitSharingNo(profitSharingNo)
                .paymentNo(paymentNo)
                .status(status)
                .success(true)
                .channelName(channelName)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建失败的分账结果
     *
     * @param profitSharingNo 分账订单号
     * @param paymentNo 原支付订单号
     * @param error 错误信息
     * @return UnifiedProfitSharingResult
     */
    public static UnifiedProfitSharingResult failed(String profitSharingNo, String paymentNo, UnifiedError error) {
        return UnifiedProfitSharingResult.builder()
                .profitSharingNo(profitSharingNo)
                .paymentNo(paymentNo)
                .status(ProfitSharingStatus.FAILED)
                .success(false)
                .error(error)
                .timestamp(System.currentTimeMillis())
                .build();
    }
}
