package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.model.config.ChannelConfig;
import net.summerfarm.payment.trade.model.sharing.SplitRule;

import java.io.Serializable;
import java.util.List;

/**
 * 统一分账请求模型
 *
 * <AUTHOR> Agent
 * @date 2025-08-27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedProfitSharingRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    // ========== 基础分账信息 ==========

    /**
     * 分账订单号（必填）
     * 商户系统内部分账订单号，要求32个字符内，只能是数字、大小写字母_-|*且在同一个商户号下唯一
     */
    private String profitSharingNo;

    /**
     * 原支付订单号（必填）
     * 需要分账的原支付订单的商户订单号
     */
    private String paymentNo;

    /**
     * 支付订单类型（必填）
     * 原支付订单的支付类型，如：ALIPAY、WXPAY等
     */
    private String payOrderType;

    /**
     * 分账规则列表（必填）
     * 包含所有分账接收方的分账信息
     */
    private List<SplitRule> splitRules;

    /**
     * 分账描述（可选）
     * 对本次分账操作的整体描述
     */
    private String description;

    /**
     * 是否是正式环境
     */
    private Boolean proEnv;

    // ========== 业务扩展字段 ==========

    /**
     * 租户ID（必填）
     */
    private Long tenantId;

    /**
     * 业务线标识（必填）
     */
    private String businessLine;

    /**
     * 渠道配置信息（必填）
     * 包含支付渠道的配置信息，用于调用渠道接口
     */
    private ChannelConfig channelConfig;
}
