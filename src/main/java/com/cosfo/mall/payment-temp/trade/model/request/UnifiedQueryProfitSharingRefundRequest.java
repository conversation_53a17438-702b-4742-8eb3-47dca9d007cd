package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.model.config.ChannelConfig;

import java.io.Serializable;

/**
 * 统一分账回退查询请求模型
 * 
 * <AUTHOR> Agent
 * @date 2025-08-27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedQueryProfitSharingRefundRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分账回退订单号（必填）
     * 需要查询的分账回退订单号
     */
    private String refundProfitSharingNo;

    /**
     * 租户ID（必填）
     */
    private Long tenantId;

    /**
     * 业务线标识（必填）
     */
    private String businessLine;

    /**
     * 是否是正式环境
     */
    private Boolean proEnv;

    /**
     * 渠道配置信息（必填）
     * 包含支付渠道的配置信息，用于调用渠道接口
     */
    private ChannelConfig channelConfig;
}
