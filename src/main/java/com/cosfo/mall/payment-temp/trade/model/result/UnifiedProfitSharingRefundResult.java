package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.common.enums.ProfitSharingRefundStatus;
import net.summerfarm.payment.trade.model.common.UnifiedError;

import java.io.Serializable;

/**
 * 统一分账回退响应模型
 *
 * <AUTHOR> Agent
 * @date 2025-08-27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedProfitSharingRefundResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分账回退订单号
     */
    private String refundProfitSharingNo;

    /**
     * 原分账订单号
     */
    private String profitSharingNo;

    /**
     * 分账回退状态
     */
    private ProfitSharingRefundStatus status;

    /**
     * 渠道分账回退订单号（如果有）
     */
    private String channelRefundOrderId;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误信息（失败时）
     */
    private UnifiedError error;

    /**
     * 响应时间戳
     */
    private Long timestamp;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 创建成功的分账回退结果
     *
     * @param refundProfitSharingNo 分账回退订单号
     * @param profitSharingNo 原分账订单号
     * @param status 分账回退状态
     * @param channelName 渠道名称
     * @return UnifiedProfitSharingRefundResult
     */
    public static UnifiedProfitSharingRefundResult success(String refundProfitSharingNo, String profitSharingNo,
                                                         ProfitSharingRefundStatus status, String channelName) {
        return UnifiedProfitSharingRefundResult.builder()
                .refundProfitSharingNo(refundProfitSharingNo)
                .profitSharingNo(profitSharingNo)
                .status(status)
                .success(true)
                .channelName(channelName)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建失败的分账回退结果
     *
     * @param refundProfitSharingNo 分账回退订单号
     * @param profitSharingNo 原分账订单号
     * @param error 错误信息
     * @return UnifiedProfitSharingRefundResult
     */
    public static UnifiedProfitSharingRefundResult failed(String refundProfitSharingNo, String profitSharingNo, UnifiedError error) {
        return UnifiedProfitSharingRefundResult.builder()
                .refundProfitSharingNo(refundProfitSharingNo)
                .profitSharingNo(profitSharingNo)
                .status(ProfitSharingRefundStatus.FAILED)
                .success(false)
                .error(error)
                .timestamp(System.currentTimeMillis())
                .build();
    }
}
