package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 分账结果详情模型
 * 
 * <AUTHOR> Agent
 * @date 2025-08-27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SplitRuleResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分账商户号
     * 接收分账资金的商户号
     */
    private String splitBillMerchantNo;

    /**
     * 分账金额（单位：元）
     * 该商户号应分得的金额
     */
    private BigDecimal splitBillAmount;

    /**
     * 分账订单号
     * 渠道生成的分账订单号
     */
    private String splitBillOrderNum;

    /**
     * 分账状态
     * DOING: 处理中
     * SUCCESS: 成功
     * FAILED: 失败
     */
    private String splitBillOrderStatus;
}
