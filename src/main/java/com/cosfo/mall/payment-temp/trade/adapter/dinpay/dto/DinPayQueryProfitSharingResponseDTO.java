package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 智付分账查询响应DTO
 * 
 * <AUTHOR> Agent
 * @date 2025-08-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DinPayQueryProfitSharingResponseDTO {

    /**
     * 分账请求订单号
     */
    private String delayOrderNo;

    /**
     * 分账状态
     * DOING: 处理中
     * SUCCESS: 成功
     * FAILED: 失败
     */
    private String delayStatus;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 交易完成时间
     * 格式：yyyy-MM-dd HH:mm:ss
     */
    private String completeTime;

    /**
     * 分账结果详情（JSON字符串）
     * 格式：[{"splitBillMerchantNo":"D10000000000001","splitBillAmount":0.3,"splitBillOrderNum":"1556013197418CBoywwwq","splitBillOrderStatus":"FAILED"}]
     */
    private String splitRules;
}
