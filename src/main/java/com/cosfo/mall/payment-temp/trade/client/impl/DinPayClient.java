package com.cosfo.mall.payment;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.payment.routing.common.enums.PaymentChannelProviderEnums;
import net.summerfarm.payment.trade.adapter.dinpay.DinPayConfig;
import net.summerfarm.payment.trade.adapter.dinpay.dto.*;
import net.summerfarm.payment.trade.client.ChannelClient;
import net.summerfarm.payment.trade.common.crypto.CertUtils;
import net.summerfarm.payment.trade.common.crypto.SM2Utils;
import net.summerfarm.payment.trade.common.crypto.SM4Utils;
import net.summerfarm.payment.trade.common.enums.DinPaymentEnum;
import net.summerfarm.payment.trade.common.enums.ErrorCode;
import net.summerfarm.payment.trade.common.exception.PaymentException;
import net.summerfarm.payment.trade.common.util.DinUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Security;

/**
 * 智付（DinPay）渠道客户端
 * 负责与智付API进行实际的HTTP交互，包括加密、签名和验签
 * <AUTHOR>
 */
@Slf4j
public class DinPayClient implements ChannelClient<DinPayRequestDTO, DinResponseDTO<DinPayResponseDTO>> {

    private static final String SIGNATURE_METHOD = "SM3WITHSM2";

    public DinPayClient() {
        Security.addProvider(new BouncyCastleProvider());
    }

    @Override
    public String getChannelCode() {
        return String.valueOf(PaymentChannelProviderEnums.DIN_PAY.getCode());
    }

    @Override
    public String getChannelName() {
        return PaymentChannelProviderEnums.DIN_PAY.getChannelName();
    }

    @Override
    public DinResponseDTO<DinPayResponseDTO> pay(DinPayRequestDTO channelRequest) throws Exception {
        log.info("执行智付支付请求，订单号：{}", channelRequest.getOrderNo());
        DinPayConfig dinPayConfig = buildDinPayConfig(channelRequest.getChannelConfig());
        String url = channelRequest.getUrl();

        return dinCommonRequest(url, channelRequest, dinPayConfig, DinPayResponseDTO.class);
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T, R> R queryPayment(T channelQueryRequest) throws Exception {
        if (!(channelQueryRequest instanceof DinPayQueryRequestDTO)) {
            throw new IllegalArgumentException("请求参数必须是DinPayQueryRequestDTO类型");
        }
        DinPayQueryRequestDTO request = (DinPayQueryRequestDTO) channelQueryRequest;
        log.info("执行智付支付查询，订单号：{}", request.getOrderNo());
        DinPayConfig dinPayConfig = buildDinPayConfig(request.getChannelConfig());
        String url = "";
        DinResponseDTO<DinPayQueryResponseDTO> response = dinCommonRequest(url, request, dinPayConfig, DinPayQueryResponseDTO.class);
        return (R) response;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T, R> R closePayment(T channelCloseRequest) throws Exception {
        if (!(channelCloseRequest instanceof DinPayCloseRequestDTO)) {
            throw new IllegalArgumentException("请求参数必须是DinPayCloseRequestDTO类型");
        }
        DinPayCloseRequestDTO request = (DinPayCloseRequestDTO) channelCloseRequest;
        log.info("执行智付关闭支付，订单号：{}", request.getPayOrderNo());
        DinPayConfig dinPayConfig = buildDinPayConfig(request.getChannelConfig());
        String url = "";
        DinResponseDTO<DinPayCloseResponseDTO> response = dinCommonRequest(url, request, dinPayConfig, DinPayCloseResponseDTO.class);
        return (R) response;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T, R> R refund(T channelRefundRequest) throws Exception {
        if (!(channelRefundRequest instanceof DinRefundRequestDTO)) {
            throw new IllegalArgumentException("请求参数必须是DinRefundRequestDTO类型");
        }
        DinRefundRequestDTO request = (DinRefundRequestDTO) channelRefundRequest;
        log.info("执行智付退款，退款订单号：{}", request.getRefundOrderNo());
        DinPayConfig dinPayConfig = buildDinPayConfig(request.getChannelConfig());
        String url = request.getUrl();
        DinResponseDTO<DinRefundResponseDTO> response = dinCommonRequest(url, request, dinPayConfig, DinRefundResponseDTO.class);
        return (R) response;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T, R> R queryRefund(T channelQueryRefundRequest) throws Exception {
        if (!(channelQueryRefundRequest instanceof DinRefundQueryRequestDTO)) {
            throw new IllegalArgumentException("请求参数必须是DinRefundQueryRequestDTO类型");
        }
        DinRefundQueryRequestDTO request = (DinRefundQueryRequestDTO) channelQueryRefundRequest;
        log.info("执行智付退款查询，退款订单号：{}", request.getRefundOrderNo());
        DinPayConfig dinPayConfig = buildDinPayConfig(request.getChannelConfig());
        String url = request.getUrl();
        DinResponseDTO<DinRefundQueryResponseDTO> response = dinCommonRequest(url, request, dinPayConfig, DinRefundQueryResponseDTO.class);
        return (R) response;
    }

    /**
     * 执行分账操作
     *
     * @param channelProfitSharingRequest 分账请求
     * @return 分账响应
     * @throws Exception 异常
     */
    public DinResponseDTO<DinPayProfitSharingResponseDTO> profitSharing(DinPayProfitSharingRequestDTO channelProfitSharingRequest) throws Exception {
        DinPayProfitSharingRequestDTO request = channelProfitSharingRequest;
        log.info("执行智付分账，分账订单号：{}，原支付订单号：{}", request.getDelayOrderNo(), request.getPayOrderNo());

        DinPayConfig dinPayConfig = buildDinPayConfig(request.getChannelConfig());
        // 从域名判断是否为生产环境，或者可以从配置中获取
        boolean isProEnv = request.getChannelConfig().getDomain() != null &&
                          request.getChannelConfig().getDomain().contains("payment.dinpay.com");
        String domain = isProEnv ? DinPaymentEnum.domain.PRO.getDomain() : DinPaymentEnum.domain.DEV.getDomain();
        String url = domain + DinPaymentEnum.url.DELAY_PROFIT_SHARING.getUrl();

        return dinCommonRequest(url, request, dinPayConfig, DinPayProfitSharingResponseDTO.class);
    }

    /**
     * 查询分账状态
     *
     * @param channelQueryProfitSharingRequest 分账查询请求
     * @return 分账查询响应
     * @throws Exception 异常
     */
    public DinResponseDTO<DinPayQueryProfitSharingResponseDTO> queryProfitSharing(DinPayQueryProfitSharingRequestDTO channelQueryProfitSharingRequest) throws Exception {
        DinPayQueryProfitSharingRequestDTO request = channelQueryProfitSharingRequest;
        log.info("查询智付分账状态，分账订单号：{}", request.getDelayOrderNo());

        DinPayConfig dinPayConfig = buildDinPayConfig(request.getChannelConfig());
        // 从域名判断是否为生产环境，或者可以从配置中获取
        boolean isProEnv = request.getChannelConfig().getDomain() != null &&
                          request.getChannelConfig().getDomain().contains("payment.dinpay.com");
        String domain = isProEnv ? DinPaymentEnum.domain.PRO.getDomain() : DinPaymentEnum.domain.DEV.getDomain();
        String url = domain + DinPaymentEnum.url.DELAY_PROFIT_SHARING_QUERY.getUrl();

        return dinCommonRequest(url, request, dinPayConfig, DinPayQueryProfitSharingResponseDTO.class);
    }

    /**
     * 执行分账回退操作
     *
     * @param request 分账回退请求
     * @return 分账回退响应
     * @throws Exception 异常
     */
    public DinResponseDTO<DinPayProfitSharingRefundResponseDTO> refundProfitSharing(DinPayProfitSharingRefundRequestDTO request) throws Exception {
        log.info("执行智付分账回退，分账回退订单号：{}，原分账订单号：{}", request.getRefundOrderNo(), request.getDelayOrderNo());

        DinPayConfig dinPayConfig = buildDinPayConfig(request.getChannelConfig());
        // 从域名判断是否为生产环境，或者可以从配置中获取
        boolean isProEnv = request.getChannelConfig().getDomain() != null &&
                          request.getChannelConfig().getDomain().contains("payment.dinpay.com");
        String domain = isProEnv ? DinPaymentEnum.domain.PRO.getDomain() : DinPaymentEnum.domain.DEV.getDomain();
        String url = domain + DinPaymentEnum.url.DELAY_PROFIT_SHARING_REFUND.getUrl();

        return dinCommonRequest(url, request, dinPayConfig, DinPayProfitSharingRefundResponseDTO.class);
    }

    /**
     * 查询分账回退状态
     *
     * @param channelQueryProfitSharingRefundRequest 分账回退查询请求
     * @return 分账回退查询响应
     * @throws Exception 异常
     */
    public DinResponseDTO<DinPayQueryProfitSharingRefundResponseDTO> queryProfitSharingRefund(DinPayQueryProfitSharingRefundRequestDTO channelQueryProfitSharingRefundRequest) throws Exception {
        DinPayQueryProfitSharingRefundRequestDTO request = channelQueryProfitSharingRefundRequest;
        log.info("查询智付分账回退状态，分账回退订单号：{}", request.getRefundOrderNo());

        DinPayConfig dinPayConfig = buildDinPayConfig(request.getChannelConfig());
        // 从域名判断是否为生产环境，或者可以从配置中获取
        boolean isProEnv = request.getChannelConfig().getDomain() != null &&
                          request.getChannelConfig().getDomain().contains("payment.dinpay.com");
        String domain = isProEnv ? DinPaymentEnum.domain.PRO.getDomain() : DinPaymentEnum.domain.DEV.getDomain();
        String url = domain + DinPaymentEnum.url.DELAY_PROFIT_SHARING_REFUND_QUERY.getUrl();

        return dinCommonRequest(url, request, dinPayConfig, DinPayQueryProfitSharingRefundResponseDTO.class);
    }

    private <T, R> DinResponseDTO<R> dinCommonRequest(String url, T requestData, DinPayConfig dinPayConfig, Class<R> responseClass) {
        PrivateKey merchantPrivateKey = CertUtils.getPrivateKeyByBase64(dinPayConfig.getPrivateKey());
        PublicKey platformPublicKey = CertUtils.getPublicKeyByBase64(dinPayConfig.getPublicKey());
        String encryptionKey = dinPayConfig.getSecret();
        String encryptedEncryptionKey = SM2Utils.encryptToBase64(platformPublicKey, encryptionKey);
        String requestJson = JSON.toJSONString(requestData);
        log.info("智付业务请求参数：{}", requestJson);
        String encryptedData = SM4Utils.encryptBase64(requestJson, encryptionKey);
        String signature = SM2Utils.sign(merchantPrivateKey, encryptedData);
        DinRequestDTO dinRequest = new DinRequestDTO(dinPayConfig.getMerchantNo(), encryptedEncryptionKey, SIGNATURE_METHOD, signature, String.format("%014d", System.currentTimeMillis()), encryptedData);
        String requestBody = JSON.toJSONString(dinRequest);
        DinResponseDTO<R> response = DinUtils.executeRequest(url, requestBody, responseClass, platformPublicKey);
        checkDinResponse(response);
        return response;
    }

    /**
     * 从ChannelConfig构建DinPayConfig
     */
    private DinPayConfig buildDinPayConfig(net.summerfarm.payment.trade.model.config.ChannelConfig channelConfig) {
        if (channelConfig == null) {
            throw new IllegalArgumentException("渠道配置不能为空");
        }

        DinPayConfig dinPayConfig = new DinPayConfig();

        // 从基本配置字段设置
        dinPayConfig.setMerchantNo(channelConfig.getMerchantNo());
        dinPayConfig.setSecret(channelConfig.getSecretKey());
        dinPayConfig.setPublicKey(channelConfig.getPublicKey());
        dinPayConfig.setPrivateKey(channelConfig.getPrivateKey());
        return dinPayConfig;
    }

    private void checkDinResponse(DinResponseDTO<?> response) {
        if (!DinPaymentEnum.responseCode.SUCCESS.getCode().equals(response.getCode()) && !DinPaymentEnum.responseCode.REFUND_RECEIVE_SUCCESS.getCode().equals(response.getCode())) {
            log.error("智付请求失败，错误码：{}，错误信息：{}", response.getCode(), response.getMsg());
            throw new PaymentException(ErrorCode.CHANNEL_UNAVAILABLE, String.format("智付渠道返回错误：错误码=%s，错误信息=%s", response.getCode(), response.getMsg()));
        }
        if (response.getData() == null) {
            log.error("智付响应数据为空，响应内容：{}", response);
            throw new PaymentException(ErrorCode.CHANNEL_UNAVAILABLE, "智付响应数据为空");
        }
    }
}