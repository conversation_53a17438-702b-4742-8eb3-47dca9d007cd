package com.cosfo.mall.payment;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.model.config.ChannelConfig;

/**
 * 智付分账回退查询请求DTO
 * 
 * <AUTHOR> Agent
 * @date 2025-08-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DinPayQueryProfitSharingRefundRequestDTO {

    /**
     * 接口名称
     */
    private String interfaceName;

    /**
     * 商户请求分账退回订单号
     */
    private String refundOrderNo;

    /**
     * 渠道配置信息（不参与JSON序列化）
     */
    @JSONField(serialize = false, deserialize = false)
    private ChannelConfig channelConfig;
}
