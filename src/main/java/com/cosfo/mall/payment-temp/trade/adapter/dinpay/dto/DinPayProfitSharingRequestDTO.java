package com.cosfo.mall.payment;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.model.config.ChannelConfig;

/**
 * 智付分账请求DTO
 * 
 * <AUTHOR> Agent
 * @date 2025-08-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DinPayProfitSharingRequestDTO {

    /**
     * 接口名称
     */
    private String interfaceName;

    /**
     * 分账请求订单号
     */
    private String delayOrderNo;

    /**
     * 支付订单号
     */
    private String payOrderNo;

    /**
     * 支付订单类型
     */
    private String payOrderType;

    /**
     * 分账规则（JSON格式字符串）
     * 格式：[{"splitBillMerchantNo":"D10000000000001","splitBillAmount":0.3}]
     */
    private String splitRule;

    /**
     * 渠道配置信息（不参与JSON序列化）
     */
    @JSONField(serialize = false, deserialize = false)
    private ChannelConfig channelConfig;
}
