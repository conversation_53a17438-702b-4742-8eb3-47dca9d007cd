package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.common.enums.ProfitSharingStatus;
import net.summerfarm.payment.trade.model.common.UnifiedError;
import net.summerfarm.payment.trade.model.sharing.SplitRuleResult;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 统一分账查询响应模型
 * 
 * <AUTHOR> Agent
 * @date 2025-08-27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedQueryProfitSharingResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分账订单号
     */
    private String profitSharingNo;

    /**
     * 原支付订单号
     */
    private String paymentNo;

    /**
     * 分账状态
     */
    private ProfitSharingStatus status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 交易完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 分账结果详情列表
     */
    private List<SplitRuleResult> splitRules;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误信息（失败时）
     */
    private UnifiedError error;

    /**
     * 响应时间戳
     */
    private Long timestamp;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 创建成功的分账查询结果
     * 
     * @param profitSharingNo 分账订单号
     * @param paymentNo 原支付订单号
     * @param status 分账状态
     * @param statusDesc 状态描述
     * @param completeTime 完成时间
     * @param splitRules 分账结果详情
     * @param channelName 渠道名称
     * @return UnifiedQueryProfitSharingResult
     */
    public static UnifiedQueryProfitSharingResult success(String profitSharingNo, String paymentNo, 
                                                        ProfitSharingStatus status, String statusDesc,
                                                        LocalDateTime completeTime, List<SplitRuleResult> splitRules,
                                                        String channelName) {
        return UnifiedQueryProfitSharingResult.builder()
                .profitSharingNo(profitSharingNo)
                .paymentNo(paymentNo)
                .status(status)
                .statusDesc(statusDesc)
                .completeTime(completeTime)
                .splitRules(splitRules)
                .success(true)
                .channelName(channelName)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建失败的分账查询结果
     * 
     * @param profitSharingNo 分账订单号
     * @param paymentNo 原支付订单号
     * @param error 错误信息
     * @return UnifiedQueryProfitSharingResult
     */
    public static UnifiedQueryProfitSharingResult failed(String profitSharingNo, String paymentNo, UnifiedError error) {
        return UnifiedQueryProfitSharingResult.builder()
                .profitSharingNo(profitSharingNo)
                .paymentNo(paymentNo)
                .status(ProfitSharingStatus.UNKNOWN)
                .success(false)
                .error(error)
                .timestamp(System.currentTimeMillis())
                .build();
    }
}
