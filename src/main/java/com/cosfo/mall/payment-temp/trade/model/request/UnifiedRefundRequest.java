package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.model.config.ChannelConfig;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 统一退款请求
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedRefundRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户支付订单号（必填）
     */
    private String paymentNo;

    /**
     * 商户退款订单号（必填）
     */
    private String refundNo;

    /**
     * 退款金额（必填，单位：分）
     */
    private Integer refundAmount;

    /**
     * 原支付交易的渠道交易ID（可选）
     */
    private String channelTransactionId;

    /**
     * 退款原因（可选）
     */
    private String refundReason;

    /**
     * 异步通知URL（可选）
     */
    private String notifyUrl;

    /**
     * 租户ID（必填）
     */
    private Long tenantId;

    /**
     * 渠道配置信息（必填）
     */
    private ChannelConfig channelConfig;

    /**
     * 是否是正式环境
     */
    private Boolean proEnv;
}
