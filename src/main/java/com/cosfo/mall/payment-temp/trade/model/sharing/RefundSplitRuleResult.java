package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 分账回退结果详情模型
 * 
 * <AUTHOR> Agent
 * @date 2025-08-27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefundSplitRuleResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户号
     * 分账回退的商户号
     */
    private String merchantNo;

    /**
     * 回退金额（单位：元）
     * 该商户号回退的金额
     */
    private BigDecimal refundAmount;

    /**
     * 回退状态
     * SUCCESS: 成功
     * FAILED: 失败
     * DOING: 处理中
     */
    private String refundStatus;
}
