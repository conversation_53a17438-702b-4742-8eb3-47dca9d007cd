package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.model.config.ChannelConfig;
import net.summerfarm.payment.trade.model.sharing.SplitRule;

import java.io.Serializable;
import java.util.List;

/**
 * 统一分账回退请求模型
 *
 * <AUTHOR> Agent
 * @date 2025-08-27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedProfitSharingRefundRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    // ========== 基础分账回退信息 ==========

    /**
     * 分账回退订单号（必填）
     * 商户系统内部分账回退订单号，要求32个字符内，只能是数字、大小写字母_-|*且在同一个商户号下唯一
     */
    private String refundProfitSharingNo;

    /**
     * 原分账订单号（必填）
     * 需要回退的原分账订单号
     */
    private String profitSharingNo;

    /**
     * 分账回退规则列表（必填）
     * 包含所有需要回退的分账信息
     */
    private List<SplitRule> refundSplitRules;

    /**
     * 分账回退描述（可选）
     * 对本次分账回退操作的整体描述
     */
    private String description;

    /**
     * 是否是正式环境
     */
    private Boolean proEnv;

    // ========== 业务扩展字段 ==========

    /**
     * 租户ID（必填）
     */
    private Long tenantId;

    /**
     * 业务线标识（必填）
     */
    private String businessLine;

    /**
     * 渠道配置信息（必填）
     * 包含支付渠道的配置信息，用于调用渠道接口
     */
    private ChannelConfig channelConfig;
}
