package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.common.enums.ProfitSharingRefundStatus;
import net.summerfarm.payment.trade.model.common.UnifiedError;
import net.summerfarm.payment.trade.model.sharing.RefundSplitRuleResult;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 统一分账回退查询响应模型
 * 
 * <AUTHOR> Agent
 * @date 2025-08-27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedQueryProfitSharingRefundResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分账回退订单号
     */
    private String refundProfitSharingNo;

    /**
     * 分账回退状态
     */
    private ProfitSharingRefundStatus status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 分账回退结果详情列表
     */
    private List<RefundSplitRuleResult> refundSplitRules;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误信息（失败时）
     */
    private UnifiedError error;

    /**
     * 响应时间戳
     */
    private Long timestamp;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 创建成功的分账回退查询结果
     * 
     * @param refundProfitSharingNo 分账回退订单号
     * @param status 分账回退状态
     * @param statusDesc 状态描述
     * @param completeTime 完成时间
     * @param refundSplitRules 分账回退结果详情
     * @param channelName 渠道名称
     * @return UnifiedQueryProfitSharingRefundResult
     */
    public static UnifiedQueryProfitSharingRefundResult success(String refundProfitSharingNo, 
                                                              ProfitSharingRefundStatus status, String statusDesc,
                                                              LocalDateTime completeTime, List<RefundSplitRuleResult> refundSplitRules,
                                                              String channelName) {
        return UnifiedQueryProfitSharingRefundResult.builder()
                .refundProfitSharingNo(refundProfitSharingNo)
                .status(status)
                .statusDesc(statusDesc)
                .completeTime(completeTime)
                .refundSplitRules(refundSplitRules)
                .success(true)
                .channelName(channelName)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建失败的分账回退查询结果
     * 
     * @param refundProfitSharingNo 分账回退订单号
     * @param error 错误信息
     * @return UnifiedQueryProfitSharingRefundResult
     */
    public static UnifiedQueryProfitSharingRefundResult failed(String refundProfitSharingNo, UnifiedError error) {
        return UnifiedQueryProfitSharingRefundResult.builder()
                .refundProfitSharingNo(refundProfitSharingNo)
                .status(ProfitSharingRefundStatus.UNKNOWN)
                .success(false)
                .error(error)
                .timestamp(System.currentTimeMillis())
                .build();
    }
}
