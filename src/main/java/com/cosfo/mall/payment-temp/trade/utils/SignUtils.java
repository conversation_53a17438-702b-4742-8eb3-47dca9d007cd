package com.cosfo.mall.payment;

import net.summerfarm.payment.trade.adapter.dinpay.DinPayConfig;
import net.summerfarm.payment.trade.common.crypto.CertUtils;
import net.summerfarm.payment.trade.common.crypto.SM2Utils;
import net.xianmu.common.exception.ParamsException;

import java.security.PublicKey;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-08-20
 **/
public class SignUtils {

    private SignUtils() {
    }

    /**
     * 智付验签
     * @param dinPayConfig 平台公钥
     * @param data 数据
     * @param sign 签名
     */
    public static Boolean verifySign4DinPay(DinPayConfig dinPayConfig, String data, String sign) {
        String publicKey = dinPayConfig.getPublicKey();
        PublicKey platformPublicKey = CertUtils.getPublicKeyByBase64(publicKey);

        return SM2Utils.verify(platformPublicKey, data, sign);
    }
}
