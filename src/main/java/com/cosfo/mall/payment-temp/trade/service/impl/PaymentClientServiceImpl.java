package com.cosfo.mall.payment;

import net.summerfarm.payment.routing.common.enums.PaymentChannelProviderEnums;
import net.summerfarm.payment.trade.adapter.ChannelAdapter;
import net.summerfarm.payment.trade.adapter.dinpay.DinPayAdapter;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayProfitSharingRequestDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayProfitSharingRefundRequestDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayProfitSharingRefundResponseDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayProfitSharingResponseDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayQueryProfitSharingRefundRequestDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayQueryProfitSharingRefundResponseDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayQueryProfitSharingRequestDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayQueryProfitSharingResponseDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinResponseDTO;
import net.summerfarm.payment.trade.client.ChannelClient;
import net.summerfarm.payment.trade.client.impl.DinPayClient;
import net.summerfarm.payment.trade.common.enums.ErrorCode;
import net.summerfarm.payment.trade.common.enums.FeatureType;
import net.summerfarm.payment.trade.common.exception.PaymentException;
import net.summerfarm.payment.trade.model.common.UnifiedError;
import net.summerfarm.payment.trade.model.request.*;
import net.summerfarm.payment.trade.model.request.UnifiedClosePaymentRequest;
import net.summerfarm.payment.trade.model.request.UnifiedPaymentRequest;
import net.summerfarm.payment.trade.model.request.UnifiedProfitSharingRefundRequest;
import net.summerfarm.payment.trade.model.request.UnifiedProfitSharingRequest;
import net.summerfarm.payment.trade.model.request.UnifiedQueryPaymentRequest;
import net.summerfarm.payment.trade.model.request.UnifiedQueryProfitSharingRefundRequest;
import net.summerfarm.payment.trade.model.request.UnifiedQueryProfitSharingRequest;
import net.summerfarm.payment.trade.model.request.UnifiedQueryRefundRequest;
import net.summerfarm.payment.trade.model.request.UnifiedRefundRequest;
import net.summerfarm.payment.trade.model.response.*;
import net.summerfarm.payment.trade.model.response.UnifiedClosePaymentResult;
import net.summerfarm.payment.trade.model.response.UnifiedPaymentResult;
import net.summerfarm.payment.trade.model.response.UnifiedQueryPaymentResult;
import net.summerfarm.payment.trade.model.response.UnifiedQueryRefundResult;
import net.summerfarm.payment.trade.model.response.UnifiedRefundResult;
import net.summerfarm.payment.trade.model.result.UnifiedProfitSharingResult;
import net.summerfarm.payment.trade.model.result.UnifiedProfitSharingRefundResult;
import net.summerfarm.payment.trade.model.result.UnifiedQueryProfitSharingResult;
import net.summerfarm.payment.trade.model.result.UnifiedQueryProfitSharingRefundResult;
import net.summerfarm.payment.trade.registry.ChannelRegistry;
import net.summerfarm.payment.trade.service.PaymentClientService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 统一支付客户端服务实现
 *
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Service
public class PaymentClientServiceImpl implements PaymentClientService {

    private static final Logger log = LoggerFactory.getLogger(PaymentClientServiceImpl.class);

    private final ChannelRegistry channelRegistry;

    public PaymentClientServiceImpl() {
        this.channelRegistry = new ChannelRegistry();
        this.channelRegistry.initialize();
    }



    @Override
    public net.summerfarm.payment.trade.model.response.UnifiedPaymentResult pay(net.summerfarm.payment.trade.model.request.UnifiedPaymentRequest request) {
        try {
            validatePaymentRequest(request);
            log.info("开始处理支付请求: paymentNo={}, totalAmount={}, paymentMethod={}",
                    request.getPaymentNo(), request.getTotalAmount(), request.getPaymentMethod());
            return processPayment(request);
        } catch (PaymentException e) {
            log.error("支付请求处理失败: paymentNo={}, errorCode={}, message={}",
                    request.getPaymentNo(), e.getErrorCode(), e.getDetailMessage(), e);
            return net.summerfarm.payment.trade.model.response.UnifiedPaymentResult.failed(
                    request.getPaymentNo(),
                    UnifiedError.from(e.getErrorCode(), e.getDetailMessage())
            );
        } catch (Exception e) {
            log.error("支付请求处理异常: paymentNo={}", request.getPaymentNo(), e);
            return net.summerfarm.payment.trade.model.response.UnifiedPaymentResult.failed(
                    request.getPaymentNo(),
                    UnifiedError.from(ErrorCode.SYSTEM_ERROR, e.getMessage())
            );
        }
    }

    @Override
    public net.summerfarm.payment.trade.model.response.UnifiedQueryPaymentResult queryPayment(net.summerfarm.payment.trade.model.request.UnifiedQueryPaymentRequest request) {
        try {
            validateQueryRequest(request);
            log.info("开始处理支付查询请求: paymentNo={}", request.getPaymentNo());
            return processQuery(request);
        } catch (PaymentException e) {
            log.error("支付查询处理失败: paymentNo={}, errorCode={}, message={}",
                    request.getPaymentNo(), e.getErrorCode(), e.getDetailMessage(), e);
            return net.summerfarm.payment.trade.model.response.UnifiedQueryPaymentResult.failed(
                    request.getPaymentNo(),
                    UnifiedError.from(e.getErrorCode(), e.getDetailMessage())
            );
        } catch (Exception e) {
            log.error("支付查询处理异常: paymentNo={}", request.getPaymentNo(), e);
            return net.summerfarm.payment.trade.model.response.UnifiedQueryPaymentResult.failed(
                    request.getPaymentNo(),
                    UnifiedError.from(ErrorCode.SYSTEM_ERROR, e.getMessage())
            );
        }
    }

    @Override
    public net.summerfarm.payment.trade.model.response.UnifiedClosePaymentResult closePayment(net.summerfarm.payment.trade.model.request.UnifiedClosePaymentRequest request) {
        try {
            validateCloseRequest(request);
            log.info("开始处理关单请求: paymentNo={}", request.getPaymentNo());
            return processClose(request);
        } catch (PaymentException e) {
            log.error("关单处理失败: paymentNo={}, errorCode={}, message={}",
                    request.getPaymentNo(), e.getErrorCode(), e.getDetailMessage(), e);
            return net.summerfarm.payment.trade.model.response.UnifiedClosePaymentResult.failed(
                    request.getPaymentNo(),
                    UnifiedError.from(e.getErrorCode(), e.getDetailMessage())
            );
        } catch (Exception e) {
            log.error("关单处理异常: paymentNo={}", request.getPaymentNo(), e);
            return net.summerfarm.payment.trade.model.response.UnifiedClosePaymentResult.failed(
                    request.getPaymentNo(),
                    UnifiedError.from(ErrorCode.SYSTEM_ERROR, e.getMessage())
            );
        }
    }

    @Override
    public net.summerfarm.payment.trade.model.response.UnifiedRefundResult refund(net.summerfarm.payment.trade.model.request.UnifiedRefundRequest request) {
        try {
            validateRefundRequest(request);
            log.info("开始处理退款请求: refundNo={}", request.getRefundNo());
            return processRefund(request);
        } catch (PaymentException e) {
            log.error("退款处理失败: refundNo={}, errorCode={}, message={}",
                    request.getRefundNo(), e.getErrorCode(), e.getDetailMessage(), e);
            return net.summerfarm.payment.trade.model.response.UnifiedRefundResult.failed(
                    request.getRefundNo(),
                    UnifiedError.from(e.getErrorCode(), e.getDetailMessage())
            );
        } catch (Exception e) {
            log.error("退款处理异常: refundNo={}", request.getRefundNo(), e);
            return net.summerfarm.payment.trade.model.response.UnifiedRefundResult.failed(
                    request.getRefundNo(),
                    UnifiedError.from(ErrorCode.SYSTEM_ERROR, e.getMessage())
            );
        }
    }

    @Override
    public net.summerfarm.payment.trade.model.response.UnifiedQueryRefundResult queryRefund(net.summerfarm.payment.trade.model.request.UnifiedQueryRefundRequest request) {
        try {
            validateQueryRefundRequest(request);
            log.info("开始处理退款查询请求: refundNo={}", request.getRefundNo());
            return processQueryRefund(request);
        } catch (PaymentException e) {
            log.error("退款查询处理失败: refundNo={}, errorCode={}, message={}",
                    request.getRefundNo(), e.getErrorCode(), e.getDetailMessage(), e);
            return net.summerfarm.payment.trade.model.response.UnifiedQueryRefundResult.failed(
                    request.getRefundNo(),
                    UnifiedError.from(e.getErrorCode(), e.getDetailMessage())
            );
        } catch (Exception e) {
            log.error("退款查询处理异常: refundNo={}", request.getRefundNo(), e);
            return net.summerfarm.payment.trade.model.response.UnifiedQueryRefundResult.failed(
                    request.getRefundNo(),
                    UnifiedError.from(ErrorCode.SYSTEM_ERROR, e.getMessage())
            );
        }
    }

    private <Req, Resp> net.summerfarm.payment.trade.model.response.UnifiedPaymentResult processPayment(net.summerfarm.payment.trade.model.request.UnifiedPaymentRequest request) throws Exception {
        String channelCode = request.getChannelConfig().getChannelCode();
        ChannelAdapter<Req, Resp> adapter = getChannelAdapter(channelCode);
        ChannelClient<Req, Resp> client = getChannelClient(channelCode);

        validateFeatureSupport(request, adapter);
        Req channelRequest = adapter.convertPaymentRequest(request);
        Resp channelResponse = client.pay(channelRequest);
        UnifiedPaymentResult result = adapter.convertPaymentResponse(channelResponse, request);
        log.info("支付请求处理完成: paymentNo={}, status={}, channelTransactionId={}",
                request.getPaymentNo(), result.getStatus(), result.getChannelTransactionId());
        return result;
    }

    private <Req, Resp> net.summerfarm.payment.trade.model.response.UnifiedQueryPaymentResult processQuery(net.summerfarm.payment.trade.model.request.UnifiedQueryPaymentRequest request) throws Exception {
        String channelCode = request.getChannelConfig().getChannelCode();
        ChannelAdapter<Req, Resp> adapter = getChannelAdapter(channelCode);
        ChannelClient<Req, Resp> client = getChannelClient(channelCode);

        Req channelRequest = adapter.convertQueryPaymentRequest(request);
        Resp channelResponse = client.queryPayment(channelRequest);
        UnifiedQueryPaymentResult result = adapter.convertQueryPaymentResponse(channelResponse, request);
        log.info("支付查询处理完成: paymentNo={}, status={}",
                request.getPaymentNo(), result.getStatus());
        return result;
    }

    private <Req, Resp> net.summerfarm.payment.trade.model.response.UnifiedClosePaymentResult processClose(net.summerfarm.payment.trade.model.request.UnifiedClosePaymentRequest request) throws Exception {
        String channelCode = request.getChannelConfig().getChannelCode();
        ChannelAdapter<Req, Resp> adapter = getChannelAdapter(channelCode);
        ChannelClient<Req, Resp> client = getChannelClient(channelCode);

        Req channelRequest = adapter.convertClosePaymentRequest(request);
        Resp channelResponse = client.closePayment(channelRequest);
        UnifiedClosePaymentResult result = adapter.convertClosePaymentResponse(channelResponse, request);
        log.info("关单处理完成: paymentNo={}, success={}",
                request.getPaymentNo(), result.isSuccess());
        return result;
    }

    private <Req, Resp> net.summerfarm.payment.trade.model.response.UnifiedRefundResult processRefund(net.summerfarm.payment.trade.model.request.UnifiedRefundRequest request) throws Exception {
        String channelCode = request.getChannelConfig().getChannelCode();
        ChannelAdapter<Req, Resp> adapter = getChannelAdapter(channelCode);
        ChannelClient<Req, Resp> client = getChannelClient(channelCode);

        Req channelRequest = adapter.convertRefundRequest(request);
        Resp channelResponse = client.refund(channelRequest);
        UnifiedRefundResult result = adapter.convertRefundResponse(channelResponse, request);
        log.info("退款处理完成: refundNo={}, status={}",
                request.getRefundNo(), result.getStatus());
        return result;
    }

    private <Req, Resp> net.summerfarm.payment.trade.model.response.UnifiedQueryRefundResult processQueryRefund(net.summerfarm.payment.trade.model.request.UnifiedQueryRefundRequest request) throws Exception {
        String channelCode = request.getChannelConfig().getChannelCode();
        ChannelAdapter<Req, Resp> adapter = getChannelAdapter(channelCode);
        ChannelClient<Req, Resp> client = getChannelClient(channelCode);

        Req channelRequest = adapter.convertQueryRefundRequest(request);
        Resp channelResponse = client.queryRefund(channelRequest);
        UnifiedQueryRefundResult result = adapter.convertQueryRefundResponse(channelResponse, request);
        log.info("退款查询处理完成: refundNo={}, status={}",
                request.getRefundNo(), result.getStatus());
        return result;
    }

    private void validateRefundRequest(UnifiedRefundRequest request) {
        if (request == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "退款请求不能为空");
        }
        if (request.getRefundNo() == null || request.getRefundNo().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "退款订单号不能为空");
        }
        if (request.getPaymentNo() == null || request.getPaymentNo().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "原支付订单号不能为空");
        }
        if (request.getRefundAmount() == null || request.getRefundAmount() <= 0) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "退款金额必须大于0");
        }
        if (request.getChannelConfig() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "渠道配置不能为空");
        }
    }

    private void validateQueryRefundRequest(UnifiedQueryRefundRequest request) {
        if (request == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "退款查询请求不能为空");
        }
        if (request.getRefundNo() == null || request.getRefundNo().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "退款订单号不能为空");
        }
        if (request.getChannelConfig() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "渠道配置不能为空");
        }
    }

    private void validateCloseRequest(UnifiedClosePaymentRequest request) {
        if (request == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "关单请求不能为空");
        }
        if (request.getPaymentNo() == null || request.getPaymentNo().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "商户订单号不能为空");
        }
        if (request.getChannelConfig() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "渠道配置不能为空");
        }
    }

    private void validateQueryRequest(UnifiedQueryPaymentRequest request) {
        if (request == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "查询请求不能为空");
        }
        if (request.getPaymentNo() == null || request.getPaymentNo().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "商户订单号不能为空");
        }
        if (request.getChannelConfig() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "渠道配置不能为空");
        }
    }

    private void validatePaymentRequest(net.summerfarm.payment.trade.model.request.UnifiedPaymentRequest request) {
        if (request == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "支付请求不能为空");
        }
        if (request.getPaymentNo() == null || request.getPaymentNo().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "商户订单号不能为空");
        }
        if (request.getTotalAmount() == null || request.getTotalAmount() <= 0) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "支付金额必须大于0");
        }
        if (request.getDescription() == null || request.getDescription().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "商品描述不能为空");
        }
        if (request.getPaymentMethod() == null || request.getPaymentMethod().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "支付方式不能为空");
        }
        if (request.getPlatform() == null || request.getPlatform().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "应用平台不能为空");
        }
        if (request.getChannelConfig() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "渠道配置不能为空");
        }
        if (request.getPayer() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "支付者信息不能为空");
        }
        if (request.getPayer().getUserId() == null || request.getPayer().getUserId().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "支付者用户ID不能为空");
        }
    }

    private <Req, Resp> ChannelAdapter<Req, Resp> getChannelAdapter(String channelCode) {
        return channelRegistry.getAdapter(channelCode);
    }

    private <Req, Resp> ChannelClient<Req, Resp> getChannelClient(String channelCode) {
        return channelRegistry.getClient(channelCode);
    }

    private void validateFeatureSupport(UnifiedPaymentRequest request, ChannelAdapter<?, ?> adapter) {
        if (request.getTotalAmount() != null && request.getTotalAmount() == 1) {
            if (!adapter.supportsFeature(FeatureType.ALLOW_ONE_CENT_ORDER)) {
                throw new PaymentException(ErrorCode.CHANNEL_FEATURE_NOT_SUPPORTED,
                        "不支持一分钱订单");
            }
        }
    }

    @Override
    public UnifiedProfitSharingResult profitSharing(net.summerfarm.payment.trade.model.request.UnifiedProfitSharingRequest request) {
        // 提前检查 request 是否为 null
        if (request == null) {
            log.error("分账请求处理失败: request为空");
            return UnifiedProfitSharingResult.failed(
                    null,
                    null,
                    UnifiedError.from(ErrorCode.INVALID_PARAMETER, "分账请求不能为空")
            );
        }

        try {
            validateProfitSharingRequest(request);
            log.info("开始处理分账请求: profitSharingNo={}, paymentNo={}, splitRulesCount={}",
                    request.getProfitSharingNo(), request.getPaymentNo(),
                    request.getSplitRules().size());
            return processProfitSharing(request);
        } catch (PaymentException e) {
            log.error("分账请求处理失败: profitSharingNo={}, errorCode={}, message={}",
                    request.getProfitSharingNo(), e.getErrorCode(), e.getDetailMessage(), e);
            return UnifiedProfitSharingResult.failed(
                    request.getProfitSharingNo(),
                    request.getPaymentNo(),
                    UnifiedError.from(e.getErrorCode(), e.getDetailMessage())
            );
        } catch (Exception e) {
            log.error("分账请求处理异常: profitSharingNo={}", request.getProfitSharingNo(), e);
            return UnifiedProfitSharingResult.failed(
                    request.getProfitSharingNo(),
                    request.getPaymentNo(),
                    UnifiedError.from(ErrorCode.SYSTEM_ERROR, e.getMessage())
            );
        }
    }

    private UnifiedProfitSharingResult processProfitSharing(net.summerfarm.payment.trade.model.request.UnifiedProfitSharingRequest request) throws Exception {
        String channelCode = request.getChannelConfig().getChannelCode();

        // 目前只支持智付渠道
        if (!String.valueOf(PaymentChannelProviderEnums.DIN_PAY.getCode()).equals(channelCode)) {
            throw new PaymentException(ErrorCode.CHANNEL_NOT_SUPPORTED, "暂不支持该渠道的分账功能");
        }

        return processDinPayProfitSharing(request);
    }

    private UnifiedProfitSharingResult processDinPayProfitSharing(net.summerfarm.payment.trade.model.request.UnifiedProfitSharingRequest request) throws Exception {
        // 直接创建智付适配器和客户端实例
        DinPayAdapter dinPayAdapter = new DinPayAdapter();
        DinPayClient dinPayClient = new DinPayClient();

        // 智付渠道支持延迟分账，无需额外验证

        // 直接调用智付分账方法
        DinPayProfitSharingRequestDTO channelRequest = dinPayAdapter.convertProfitSharingRequest(request);
        DinResponseDTO<DinPayProfitSharingResponseDTO> channelResponse = dinPayClient.profitSharing(channelRequest);
        UnifiedProfitSharingResult result = dinPayAdapter.convertProfitSharingResponse(channelResponse, request);

        log.info("分账请求处理完成: profitSharingNo={}, status={}",
                request.getProfitSharingNo(), result.getStatus());
        return result;
    }



    private void validateProfitSharingRequest(UnifiedProfitSharingRequest request) {
        // request 已经在调用方法中检查过了，这里不需要再检查
        if (request.getProfitSharingNo() == null || request.getProfitSharingNo().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "分账订单号不能为空");
        }
        if (request.getPaymentNo() == null || request.getPaymentNo().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "原支付订单号不能为空");
        }
        if (request.getPayOrderType() == null || request.getPayOrderType().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "支付订单类型不能为空");
        }
        if (request.getSplitRules() == null || request.getSplitRules().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "分账规则不能为空");
        }
        if (request.getChannelConfig() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "渠道配置不能为空");
        }
        if (request.getTenantId() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "租户ID不能为空");
        }
        if (request.getBusinessLine() == null || request.getBusinessLine().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "业务线标识不能为空");
        }

        // 验证分账规则
        request.getSplitRules().forEach(rule -> {
            if (rule.getMerchantNo() == null || rule.getMerchantNo().trim().isEmpty()) {
                throw new PaymentException(ErrorCode.PROFIT_SHARING_RULE_ERROR, "分账商户号不能为空");
            }
            if (rule.getAmount() == null || rule.getAmount().compareTo(java.math.BigDecimal.ZERO) <= 0) {
                throw new PaymentException(ErrorCode.PROFIT_SHARING_RULE_ERROR, "分账金额必须大于0");
            }
        });
    }

    @Override
    public UnifiedQueryProfitSharingResult queryProfitSharing(net.summerfarm.payment.trade.model.request.UnifiedQueryProfitSharingRequest request) {
        // 提前检查 request 是否为 null
        if (request == null) {
            log.error("分账查询请求处理失败: request为空");
            return UnifiedQueryProfitSharingResult.failed(
                    null,
                    null,
                    UnifiedError.from(ErrorCode.INVALID_PARAMETER, "分账查询请求不能为空")
            );
        }

        try {
            validateQueryProfitSharingRequest(request);
            log.info("开始处理分账查询请求: profitSharingNo={}, paymentNo={}",
                    request.getProfitSharingNo(), request.getPaymentNo());
            return processQueryProfitSharing(request);
        } catch (PaymentException e) {
            log.error("分账查询请求处理失败: profitSharingNo={}, errorCode={}, message={}",
                    request.getProfitSharingNo(), e.getErrorCode(), e.getDetailMessage(), e);
            return UnifiedQueryProfitSharingResult.failed(
                    request.getProfitSharingNo(),
                    request.getPaymentNo(),
                    UnifiedError.from(e.getErrorCode(), e.getDetailMessage())
            );
        } catch (Exception e) {
            log.error("分账查询请求处理异常: profitSharingNo={}", request.getProfitSharingNo(), e);
            return UnifiedQueryProfitSharingResult.failed(
                    request.getProfitSharingNo(),
                    request.getPaymentNo(),
                    UnifiedError.from(ErrorCode.SYSTEM_ERROR, e.getMessage())
            );
        }
    }

    private UnifiedQueryProfitSharingResult processQueryProfitSharing(net.summerfarm.payment.trade.model.request.UnifiedQueryProfitSharingRequest request) throws Exception {
        String channelCode = request.getChannelConfig().getChannelCode();

        // 目前只支持智付渠道
        if (!String.valueOf(PaymentChannelProviderEnums.DIN_PAY.getCode()).equals(channelCode)) {
            throw new PaymentException(ErrorCode.CHANNEL_NOT_SUPPORTED, "暂不支持该渠道的分账查询功能");
        }

        return processDinPayQueryProfitSharing(request);
    }

    private UnifiedQueryProfitSharingResult processDinPayQueryProfitSharing(net.summerfarm.payment.trade.model.request.UnifiedQueryProfitSharingRequest request) throws Exception {
        // 直接创建智付适配器和客户端实例
        DinPayAdapter dinPayAdapter = new DinPayAdapter();
        DinPayClient dinPayClient = new DinPayClient();

        // 智付渠道支持分账查询，无需额外验证

        // 直接调用智付分账查询方法
        DinPayQueryProfitSharingRequestDTO channelRequest = dinPayAdapter.convertQueryProfitSharingRequest(request);
        DinResponseDTO<DinPayQueryProfitSharingResponseDTO> channelResponse = dinPayClient.queryProfitSharing(channelRequest);
        UnifiedQueryProfitSharingResult result = dinPayAdapter.convertQueryProfitSharingResponse(channelResponse, request);

        log.info("分账查询请求处理完成: profitSharingNo={}, status={}",
                request.getProfitSharingNo(), result.getStatus());
        return result;
    }

    private void validateQueryProfitSharingRequest(UnifiedQueryProfitSharingRequest request) {
        // request 已经在调用方法中检查过了，这里不需要再检查
        if (request.getProfitSharingNo() == null || request.getProfitSharingNo().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "分账订单号不能为空");
        }
        if (request.getChannelConfig() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "渠道配置不能为空");
        }
        if (request.getTenantId() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "租户ID不能为空");
        }
        if (request.getBusinessLine() == null || request.getBusinessLine().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "业务线标识不能为空");
        }
    }

    @Override
    public UnifiedProfitSharingRefundResult refundProfitSharing(net.summerfarm.payment.trade.model.request.UnifiedProfitSharingRefundRequest request) {
        // 提前检查 request 是否为 null
        if (request == null) {
            log.error("分账回退请求处理失败: request为空");
            return UnifiedProfitSharingRefundResult.failed(
                    null,
                    null,
                    UnifiedError.from(ErrorCode.INVALID_PARAMETER, "分账回退请求不能为空")
            );
        }

        try {
            validateProfitSharingRefundRequest(request);
            log.info("开始处理分账回退请求: refundProfitSharingNo={}, profitSharingNo={}, refundRulesCount={}",
                    request.getRefundProfitSharingNo(), request.getProfitSharingNo(),
                    request.getRefundSplitRules().size());
            return processProfitSharingRefund(request);
        } catch (PaymentException e) {
            log.error("分账回退请求处理失败: refundProfitSharingNo={}, errorCode={}, message={}",
                    request.getRefundProfitSharingNo(), e.getErrorCode(), e.getDetailMessage(), e);
            return UnifiedProfitSharingRefundResult.failed(
                    request.getRefundProfitSharingNo(),
                    request.getProfitSharingNo(),
                    UnifiedError.from(e.getErrorCode(), e.getDetailMessage())
            );
        } catch (Exception e) {
            log.error("分账回退请求处理异常: refundProfitSharingNo={}", request.getRefundProfitSharingNo(), e);
            return UnifiedProfitSharingRefundResult.failed(
                    request.getRefundProfitSharingNo(),
                    request.getProfitSharingNo(),
                    UnifiedError.from(ErrorCode.SYSTEM_ERROR, e.getMessage())
            );
        }
    }

    private UnifiedProfitSharingRefundResult processProfitSharingRefund(net.summerfarm.payment.trade.model.request.UnifiedProfitSharingRefundRequest request) throws Exception {
        String channelCode = request.getChannelConfig().getChannelCode();

        // 目前只支持智付渠道
        if (!String.valueOf(PaymentChannelProviderEnums.DIN_PAY.getCode()).equals(channelCode)) {
            throw new PaymentException(ErrorCode.CHANNEL_NOT_SUPPORTED, "暂不支持该渠道的分账回退功能");
        }

        return processDinPayProfitSharingRefund(request);
    }

    private UnifiedProfitSharingRefundResult processDinPayProfitSharingRefund(net.summerfarm.payment.trade.model.request.UnifiedProfitSharingRefundRequest request) throws Exception {
        // 直接创建智付适配器和客户端实例
        DinPayAdapter dinPayAdapter = new DinPayAdapter();
        DinPayClient dinPayClient = new DinPayClient();

        // 智付渠道支持分账回退，无需额外验证

        // 直接调用智付分账回退方法
        DinPayProfitSharingRefundRequestDTO channelRequest = dinPayAdapter.convertProfitSharingRefundRequest(request);
        DinResponseDTO<DinPayProfitSharingRefundResponseDTO> channelResponse = dinPayClient.refundProfitSharing(channelRequest);
        UnifiedProfitSharingRefundResult result = dinPayAdapter.convertProfitSharingRefundResponse(channelResponse, request);

        log.info("分账回退请求处理完成: refundProfitSharingNo={}, status={}",
                request.getRefundProfitSharingNo(), result.getStatus());
        return result;
    }

    private void validateProfitSharingRefundRequest(UnifiedProfitSharingRefundRequest request) {
        // request 已经在调用方法中检查过了，这里不需要再检查
        if (request.getRefundProfitSharingNo() == null || request.getRefundProfitSharingNo().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "分账回退订单号不能为空");
        }
        if (request.getProfitSharingNo() == null || request.getProfitSharingNo().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "原分账订单号不能为空");
        }
        if (request.getRefundSplitRules() == null || request.getRefundSplitRules().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "分账回退规则不能为空");
        }
        if (request.getChannelConfig() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "渠道配置不能为空");
        }
        if (request.getTenantId() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "租户ID不能为空");
        }
        if (request.getBusinessLine() == null || request.getBusinessLine().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "业务线标识不能为空");
        }

        // 验证分账回退规则
        request.getRefundSplitRules().forEach(rule -> {
            if (rule.getMerchantNo() == null || rule.getMerchantNo().trim().isEmpty()) {
                throw new PaymentException(ErrorCode.PROFIT_SHARING_RULE_ERROR, "分账回退商户号不能为空");
            }
            if (rule.getAmount() == null || rule.getAmount().compareTo(java.math.BigDecimal.ZERO) <= 0) {
                throw new PaymentException(ErrorCode.PROFIT_SHARING_RULE_ERROR, "分账回退金额必须大于0");
            }
        });
    }

    @Override
    public UnifiedQueryProfitSharingRefundResult queryProfitSharingRefund(net.summerfarm.payment.trade.model.request.UnifiedQueryProfitSharingRefundRequest request) {
        // 提前检查 request 是否为 null
        if (request == null) {
            log.error("分账回退查询请求处理失败: request为空");
            return UnifiedQueryProfitSharingRefundResult.failed(
                    null,
                    UnifiedError.from(ErrorCode.INVALID_PARAMETER, "分账回退查询请求不能为空")
            );
        }

        try {
            validateQueryProfitSharingRefundRequest(request);
            log.info("开始处理分账回退查询请求: refundProfitSharingNo={}",
                    request.getRefundProfitSharingNo());
            return processQueryProfitSharingRefund(request);
        } catch (PaymentException e) {
            log.error("分账回退查询请求处理失败: refundProfitSharingNo={}, errorCode={}, message={}",
                    request.getRefundProfitSharingNo(), e.getErrorCode(), e.getDetailMessage(), e);
            return UnifiedQueryProfitSharingRefundResult.failed(
                    request.getRefundProfitSharingNo(),
                    UnifiedError.from(e.getErrorCode(), e.getDetailMessage())
            );
        } catch (Exception e) {
            log.error("分账回退查询请求处理异常: refundProfitSharingNo={}", request.getRefundProfitSharingNo(), e);
            return UnifiedQueryProfitSharingRefundResult.failed(
                    request.getRefundProfitSharingNo(),
                    UnifiedError.from(ErrorCode.SYSTEM_ERROR, e.getMessage())
            );
        }
    }

    private UnifiedQueryProfitSharingRefundResult processQueryProfitSharingRefund(net.summerfarm.payment.trade.model.request.UnifiedQueryProfitSharingRefundRequest request) throws Exception {
        String channelCode = request.getChannelConfig().getChannelCode();

        // 目前只支持智付渠道
        if (!String.valueOf(PaymentChannelProviderEnums.DIN_PAY.getCode()).equals(channelCode)) {
            throw new PaymentException(ErrorCode.CHANNEL_NOT_SUPPORTED, "暂不支持该渠道的分账回退查询功能");
        }

        return processDinPayQueryProfitSharingRefund(request);
    }

    private UnifiedQueryProfitSharingRefundResult processDinPayQueryProfitSharingRefund(net.summerfarm.payment.trade.model.request.UnifiedQueryProfitSharingRefundRequest request) throws Exception {
        // 直接创建智付适配器和客户端实例
        DinPayAdapter dinPayAdapter = new DinPayAdapter();
        DinPayClient dinPayClient = new DinPayClient();

        // 智付渠道支持分账回退查询，无需额外验证

        // 直接调用智付分账回退查询方法
        DinPayQueryProfitSharingRefundRequestDTO channelRequest = dinPayAdapter.convertQueryProfitSharingRefundRequest(request);
        DinResponseDTO<DinPayQueryProfitSharingRefundResponseDTO> channelResponse = dinPayClient.queryProfitSharingRefund(channelRequest);
        UnifiedQueryProfitSharingRefundResult result = dinPayAdapter.convertQueryProfitSharingRefundResponse(channelResponse, request);

        log.info("分账回退查询请求处理完成: refundProfitSharingNo={}, status={}",
                request.getRefundProfitSharingNo(), result.getStatus());
        return result;
    }

    private void validateQueryProfitSharingRefundRequest(UnifiedQueryProfitSharingRefundRequest request) {
        // request 已经在调用方法中检查过了，这里不需要再检查
        if (request.getRefundProfitSharingNo() == null || request.getRefundProfitSharingNo().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "分账回退订单号不能为空");
        }
        if (request.getChannelConfig() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "渠道配置不能为空");
        }
        if (request.getTenantId() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "租户ID不能为空");
        }
        if (request.getBusinessLine() == null || request.getBusinessLine().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "业务线标识不能为空");
        }
    }
}
