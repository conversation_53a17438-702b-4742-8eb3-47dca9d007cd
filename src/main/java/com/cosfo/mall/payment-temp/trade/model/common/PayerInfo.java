package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 支付者信息
 *
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PayerInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户标识（必填）
     * 用户在商户appid下的唯一标识
     * 微信支付：用户在商户appid下的唯一标识openid
     * 支付宝：买家支付宝账号对应的支付宝唯一用户号buyer_id
     */
    private String userId;

    /**
     * 用户终端IP（必填）
     * 支持IPV4和IPV6两种格式的IP地址
     */
    private String ip;
}
