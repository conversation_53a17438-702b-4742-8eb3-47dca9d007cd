package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款状态枚举
 */
@Getter
@AllArgsConstructor
public enum RefundStatus {

    /**
     * 退款成功
     */
    SUCCESS("SUCCESS", "退款成功"),

    /**
     * 退款失败
     */
    FAILED("FAILED", "退款失败"),

    /**
     * 退款处理中
     */
    PENDING("PENDING", "退款处理中"),

    /**
     * 退款关闭
     */
    CLOSED("CLOSED", "退款关闭"),

    /**
     * 未知状态
     */
    UNKNOWN("UNKNOWN", "未知状态");

    private final String code;
    private final String description;

    public static RefundStatus fromCode(String code) {
        for (RefundStatus status : RefundStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return UNKNOWN;
    }
}
