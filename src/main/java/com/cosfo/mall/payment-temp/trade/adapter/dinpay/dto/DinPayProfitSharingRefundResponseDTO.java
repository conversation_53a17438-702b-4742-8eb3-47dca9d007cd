package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 智付分账回退响应DTO
 * 
 * <AUTHOR> Agent
 * @date 2025-08-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DinPayProfitSharingRefundResponseDTO {

    /**
     * 商户请求分账退回订单号
     */
    private String refundOrderNo;

    /**
     * 分账回退状态
     * INIT: 初始状态
     * DOING: 处理中
     * SUCCESS: 成功
     * FAILED: 失败
     */
    private String refundStatus;
}
