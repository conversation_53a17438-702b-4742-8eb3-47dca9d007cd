package com.cosfo.mall.payment;

import net.summerfarm.payment.trade.model.config.ChannelConfig;
import net.summerfarm.payment.trade.model.response.NotificationResult;

/**
 * 支付通知服务接口
 * 负责接收和处理来自支付渠道的异步通知
 */
public interface PaymentNotificationService {

    /**
     * 处理支付渠道的异步通知
     *
     * @param channelCode 渠道标识符
     * @param rawNotificationData 原始通知数据（通常是HTTP请求体）
     * @param config 渠道配置信息，用于验签等
     * @return 通知处理结果
     */
    NotificationResult handleNotification(String channelCode, String rawNotificationData, ChannelConfig config);
}
