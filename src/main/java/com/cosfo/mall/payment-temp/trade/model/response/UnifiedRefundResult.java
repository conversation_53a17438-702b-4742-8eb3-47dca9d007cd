package com.cosfo.mall.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.common.enums.RefundStatus;
import net.summerfarm.payment.trade.model.common.UnifiedError;

import java.io.Serializable;

/**
 * 统一退款结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedRefundResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 退款状态
     */
    private RefundStatus status;

    /**
     * 商户退款订单号
     */
    private String refundNo;

    /**
     * 渠道返回的退款交易ID
     */
    private String channelRefundId;

    /**
     * 渠道返回的原始报文
     */
    private String rawResponse;

    /**
     * 错误信息（失败时才有）
     */
    private UnifiedError error;

    /**
     * 创建一个失败的退款结果
     */
    public static UnifiedRefundResult failed(String refundNo, UnifiedError error) {
        return UnifiedRefundResult.builder()
                .status(RefundStatus.FAILED)
                .refundNo(refundNo)
                .error(error)
                .build();
    }
}
