package com.cosfo.mall.payment;

import com.alibaba.fastjson.JSON;
import net.summerfarm.payment.routing.common.enums.PaymentChannelProviderEnums;
import net.summerfarm.payment.routing.common.enums.PaymentDictionaryEnums;
import net.summerfarm.payment.routing.common.enums.PaymentMethodEnums;
import net.summerfarm.payment.trade.adapter.ChannelAdapter;
import net.summerfarm.payment.trade.adapter.dinpay.dto.*;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayCloseRequestDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayCloseResponseDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayProfitSharingRefundRequestDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayProfitSharingRefundResponseDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayProfitSharingRequestDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayProfitSharingResponseDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayQueryProfitSharingRefundRequestDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayQueryProfitSharingRefundResponseDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayQueryProfitSharingRequestDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayQueryProfitSharingResponseDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayQueryRequestDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayQueryResponseDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayRequestDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayResponseDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinRefundQueryRequestDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinRefundQueryResponseDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinRefundRequestDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinRefundResponseDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.DinResponseDTO;
import net.summerfarm.payment.trade.common.enums.DinPaymentEnum;
import net.summerfarm.payment.trade.common.enums.FeatureType;
import net.summerfarm.payment.trade.common.enums.PaymentStatus;
import net.summerfarm.payment.trade.common.enums.ProfitSharingRefundStatus;
import net.summerfarm.payment.trade.common.enums.ProfitSharingStatus;
import net.summerfarm.payment.trade.common.enums.RefundStatus;
import net.summerfarm.payment.trade.model.common.PayerInfo;
import net.summerfarm.payment.trade.model.common.PaymentCredential;
import net.summerfarm.payment.trade.model.request.*;
import net.summerfarm.payment.trade.model.request.UnifiedClosePaymentRequest;
import net.summerfarm.payment.trade.model.request.UnifiedPaymentRequest;
import net.summerfarm.payment.trade.model.request.UnifiedProfitSharingRefundRequest;
import net.summerfarm.payment.trade.model.request.UnifiedProfitSharingRequest;
import net.summerfarm.payment.trade.model.request.UnifiedQueryPaymentRequest;
import net.summerfarm.payment.trade.model.request.UnifiedQueryProfitSharingRefundRequest;
import net.summerfarm.payment.trade.model.request.UnifiedQueryProfitSharingRequest;
import net.summerfarm.payment.trade.model.request.UnifiedQueryRefundRequest;
import net.summerfarm.payment.trade.model.request.UnifiedRefundRequest;
import net.summerfarm.payment.trade.model.response.*;
import net.summerfarm.payment.trade.model.response.UnifiedClosePaymentResult;
import net.summerfarm.payment.trade.model.response.UnifiedPaymentResult;
import net.summerfarm.payment.trade.model.response.UnifiedQueryPaymentResult;
import net.summerfarm.payment.trade.model.response.UnifiedQueryRefundResult;
import net.summerfarm.payment.trade.model.response.UnifiedRefundResult;
import net.summerfarm.payment.trade.model.result.UnifiedProfitSharingResult;
import net.summerfarm.payment.trade.model.result.UnifiedProfitSharingRefundResult;
import net.summerfarm.payment.trade.model.result.UnifiedQueryProfitSharingResult;
import net.summerfarm.payment.trade.model.result.UnifiedQueryProfitSharingRefundResult;
import net.summerfarm.payment.trade.model.sharing.RefundSplitRuleResult;
import net.summerfarm.payment.trade.model.sharing.SplitRule;
import net.summerfarm.payment.trade.model.sharing.SplitRuleResult;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 智付（DinPay）渠道适配器
 * 负责将SDK的统一模型与智付的特定模型进行互相转换
 * <AUTHOR>
 */
public class DinPayAdapter implements ChannelAdapter<net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayRequestDTO, net.summerfarm.payment.trade.adapter.dinpay.dto.DinResponseDTO<net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayResponseDTO>> {

    private static final Logger log = LoggerFactory.getLogger(DinPayAdapter.class);

    @Override
    public String getChannelCode() {
        return String.valueOf(PaymentChannelProviderEnums.DIN_PAY.getCode());
    }

    @Override
    public String getChannelName() {
        return PaymentChannelProviderEnums.DIN_PAY.getChannelName();
    }

    @Override
    public Set<FeatureType> getSupportedFeatures() {
        Set<FeatureType> features = new HashSet<>();
        features.add(FeatureType.DELAY_PROFIT_SHARING);
        features.add(FeatureType.REFUND_SUPPORT);
        features.add(FeatureType.ORDER_QUERY_SUPPORT);
        features.add(FeatureType.ORDER_CLOSE_SUPPORT);
        features.add(FeatureType.ASYNC_NOTIFY_SUPPORT);
        return features;
    }

    @Override
    public net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayRequestDTO convertPaymentRequest(net.summerfarm.payment.trade.model.request.UnifiedPaymentRequest request) {
        if (request.getChannelConfig() == null) {
            throw new IllegalArgumentException("智付渠道扩展配置不能为空");
        }
        String interfaceName = determineInterfaceName(request.getPlatform());
        String paymentMethods = determinePaymentMethods(request.getPlatform());
        String appId = request.getAppId();
        PayerInfo payer = request.getPayer();
        String openId = payer.getUserId();
        String orderIp = payer.getIp();
        BigDecimal payAmountInYuan = new BigDecimal(request.getTotalAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        String payType = Objects.equals(request.getPaymentMethod(), PaymentMethodEnums.WECHAT.getCode()) ? DinPaymentEnum.paymentType.WECHAT.getType() : DinPaymentEnum.paymentType.ALIPAY.getType();
        String url = DinPaymentEnum.url.determinePayUrl(request.getPlatform());
        url = request.getProEnv() ? DinPaymentEnum.domain.PRO.getDomain() + url : DinPaymentEnum.domain.DEV.getDomain() + url;

        return DinPayRequestDTO.builder()
                .interfaceName(interfaceName)
                .orderNo(request.getPaymentNo())
                .paymentType(payType)
                .paymentMethods(paymentMethods)
                .appid(appId)
                .openId(openId)
                .orderIp(orderIp)
                .payAmount(payAmountInYuan)
                .currency(request.getCurrency())
                .goodsName(request.getDescription())
                .notifyUrl(request.getNotifyUrl())
                .timeExpire(request.getTimeExpireSeconds())
                .orderDesc(request.getAttach())
                .channelConfig(request.getChannelConfig())
                .url(url)
                .build();
    }

    @Override
    public net.summerfarm.payment.trade.model.response.UnifiedPaymentResult convertPaymentResponse(net.summerfarm.payment.trade.adapter.dinpay.dto.DinResponseDTO<net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayResponseDTO> channelResponse, UnifiedPaymentRequest originalRequest) {
        DinPayResponseDTO dinResponseData = channelResponse.getData();
        String payInfo = dinResponseData.getPayInfo();
        PaymentCredential credential = PaymentCredential.builder()
                .content(payInfo)
                .build();

        return UnifiedPaymentResult.builder()
                .status(PaymentStatus.SUCCESS)
                .paymentNo(originalRequest.getPaymentNo())
                .channelTransactionId(dinResponseData.getOutTransactionOrderId())
                .credential(credential)
                .rawResponse(JSON.toJSONString(channelResponse))
                .timestamp(System.currentTimeMillis())
                .build();
    }

    @Override
    public net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayQueryRequestDTO convertQueryPaymentRequest(net.summerfarm.payment.trade.model.request.UnifiedQueryPaymentRequest request) {
        return DinPayQueryRequestDTO.builder()
                .interfaceName(DinPaymentEnum.interfaceName.APP_PAY_QUERY.getName())
                .orderNo(request.getPaymentNo())
                .channelNumber(request.getChannelTransactionId())
                .channelConfig(request.getChannelConfig())
                .build();
    }

    public net.summerfarm.payment.trade.model.response.UnifiedQueryPaymentResult convertQueryPaymentResponse(net.summerfarm.payment.trade.adapter.dinpay.dto.DinResponseDTO<net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayQueryResponseDTO> channelQueryResponse, UnifiedQueryPaymentRequest originalRequest) {
        DinPayQueryResponseDTO responseData = channelQueryResponse.getData();
        PaymentStatus status = convertToUnifiedStatus(responseData.getOrderStatus());
        return UnifiedQueryPaymentResult.builder()
                .status(status)
                .paymentNo(responseData.getOrderNo())
                .channelTransactionId(responseData.getOutTransactionOrderId())
                .rawResponse(JSON.toJSONString(channelQueryResponse))
                .build();
    }

    @Override
    public net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayCloseRequestDTO convertClosePaymentRequest(net.summerfarm.payment.trade.model.request.UnifiedClosePaymentRequest request) {
        return DinPayCloseRequestDTO.builder()
                .interfaceName(DinPaymentEnum.interfaceName.APP_PAY_CLOSE.getName())
                .payOrderNo(request.getPaymentNo())
                .channelConfig(request.getChannelConfig())
                .build();
    }

    public net.summerfarm.payment.trade.model.response.UnifiedClosePaymentResult convertClosePaymentResponse(net.summerfarm.payment.trade.adapter.dinpay.dto.DinResponseDTO<DinPayCloseResponseDTO> channelCloseResponse, UnifiedClosePaymentRequest originalRequest) {
        boolean isSuccess = DinPaymentEnum.responseCode.SUCCESS.getCode().equals(channelCloseResponse.getCode());
        return UnifiedClosePaymentResult.builder()
                .success(isSuccess)
                .paymentNo(originalRequest.getPaymentNo())
                .rawResponse(JSON.toJSONString(channelCloseResponse))
                .build();
    }

    @Override
    public net.summerfarm.payment.trade.adapter.dinpay.dto.DinRefundRequestDTO convertRefundRequest(net.summerfarm.payment.trade.model.request.UnifiedRefundRequest request) {
        BigDecimal refundAmountInYuan = new BigDecimal(request.getRefundAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        String domain = request.getProEnv() ? DinPaymentEnum.domain.PRO.getDomain() : DinPaymentEnum.domain.DEV.getDomain();
        String url = domain + DinPaymentEnum.url.REFUND.getUrl();
        return DinRefundRequestDTO.builder()
                .interfaceName(DinPaymentEnum.interfaceName.APP_PAY_REFUND.getName())
                .payOrderNo(request.getPaymentNo())
                .refundOrderNo(request.getRefundNo())
                .refundAmount(refundAmountInYuan)
                .notifyUrl(request.getNotifyUrl())
                .channelConfig(request.getChannelConfig())
                .url(url)
                .build();
    }

    public net.summerfarm.payment.trade.model.response.UnifiedRefundResult convertRefundResponse(net.summerfarm.payment.trade.adapter.dinpay.dto.DinResponseDTO<net.summerfarm.payment.trade.adapter.dinpay.dto.DinRefundResponseDTO> channelRefundResponse, UnifiedRefundRequest originalRequest) {
        DinRefundResponseDTO responseData = channelRefundResponse.getData();
        RefundStatus status = convertToUnifiedRefundStatus(responseData.getControlType());
        return UnifiedRefundResult.builder()
                .status(status)
                .refundNo(responseData.getRefundOrderNo())
                .channelRefundId(responseData.getRefundChannelNumber())
                .rawResponse(JSON.toJSONString(channelRefundResponse))
                .build();
    }

    @Override
    public net.summerfarm.payment.trade.adapter.dinpay.dto.DinRefundQueryRequestDTO convertQueryRefundRequest(net.summerfarm.payment.trade.model.request.UnifiedQueryRefundRequest request) {
        String domain = request.getProEnv() ? DinPaymentEnum.domain.PRO.getDomain() : DinPaymentEnum.domain.DEV.getDomain();
        String url = domain + DinPaymentEnum.url.REFUND_QUERY.getUrl();
        return DinRefundQueryRequestDTO.builder()
                .interfaceName(DinPaymentEnum.interfaceName.APP_PAY_REFUND_QUERY.getName())
                .refundOrderNo(request.getRefundNo())
                .channelConfig(request.getChannelConfig())
                .url(url)
                .build();
    }

    public net.summerfarm.payment.trade.model.response.UnifiedQueryRefundResult convertQueryRefundResponse(net.summerfarm.payment.trade.adapter.dinpay.dto.DinResponseDTO<net.summerfarm.payment.trade.adapter.dinpay.dto.DinRefundQueryResponseDTO> channelQueryRefundResponse, UnifiedQueryRefundRequest originalRequest) {
        DinRefundQueryResponseDTO responseData = channelQueryRefundResponse.getData();
        RefundStatus status = convertToUnifiedRefundStatus(responseData.getOrderStatus());
        return UnifiedQueryRefundResult.builder()
                .status(status)
                .refundNo(responseData.getRefundOrderNo())
                .channelRefundId(responseData.getRefundChannelNumber())
                .rawResponse(JSON.toJSONString(channelQueryRefundResponse))
                .build();
    }

    private RefundStatus convertToUnifiedRefundStatus(String dinPayRefundStatus) {
        if (dinPayRefundStatus == null) {
            return RefundStatus.UNKNOWN;
        }

        // 使用DinPaymentEnum中的退款状态枚举进行匹配
        if (DinPaymentEnum.refundStatus.SUCCESS.getStatus().equals(dinPayRefundStatus)) {
            return RefundStatus.SUCCESS;
        }
        if (DinPaymentEnum.refundStatus.FAIL.getStatus().equals(dinPayRefundStatus)) {
            return RefundStatus.FAILED;
        }
        if (DinPaymentEnum.refundStatus.DOING.getStatus().equals(dinPayRefundStatus) ||
            DinPaymentEnum.refundStatus.RECEIVE.getStatus().equals(dinPayRefundStatus) ||
            DinPaymentEnum.refundStatus.BEFORE_RECEIVE.getStatus().equals(dinPayRefundStatus)) {
            return RefundStatus.PENDING;
        }
        if (DinPaymentEnum.refundStatus.CLOSE.getStatus().equals(dinPayRefundStatus)) {
            return RefundStatus.CLOSED;
        }
        return RefundStatus.UNKNOWN;
    }

    private PaymentStatus convertToUnifiedStatus(String dinPayStatus) {
        if (dinPayStatus == null) {
            return PaymentStatus.UNKNOWN;
        }

        // 使用DinPaymentEnum中的订单状态枚举进行匹配
        if (DinPaymentEnum.orderStatus.SUCCESS.getStatus().equals(dinPayStatus)) {
            return PaymentStatus.SUCCESS;
        }
        if (DinPaymentEnum.orderStatus.FAIL.getStatus().equals(dinPayStatus)) {
            return PaymentStatus.FAILED;
        }
        if (DinPaymentEnum.orderStatus.INIT.getStatus().equals(dinPayStatus) ||
            DinPaymentEnum.orderStatus.DOING.getStatus().equals(dinPayStatus)) {
            return PaymentStatus.PENDING;
        }
        if (DinPaymentEnum.orderStatus.CLOSE.getStatus().equals(dinPayStatus)) {
            return PaymentStatus.CLOSED;
        }
        if (DinPaymentEnum.orderStatus.CANCEL.getStatus().equals(dinPayStatus)) {
            return PaymentStatus.CANCELLED;
        }
        return PaymentStatus.UNKNOWN;
    }

    private String determineInterfaceName(String platform) {
        if (PaymentDictionaryEnums.Platform.MINI_APP.getName().equalsIgnoreCase(platform)) {
            return DinPaymentEnum.interfaceName.MINI_APP.getName();
        }
        // 默认返回公众号支付接口名称
        return DinPaymentEnum.interfaceName.PUBLIC_ACCOUNT.getName();
    }

    private String determinePaymentMethods(String platform) {
        if (PaymentDictionaryEnums.Platform.MINI_APP.getName().equalsIgnoreCase(platform)) {
            return DinPaymentEnum.paymentMethods.MINI_APP.getMethod();
        }
        return DinPaymentEnum.paymentMethods.PUBLIC.getMethod();
    }

    // ========== 分账相关方法 ==========

    /**
     * 转换分账请求
     *
     * @param request 统一分账请求
     * @return 智付分账请求DTO
     */
    public net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayProfitSharingRequestDTO convertProfitSharingRequest(net.summerfarm.payment.trade.model.request.UnifiedProfitSharingRequest request) {
        if (request.getChannelConfig() == null) {
            throw new IllegalArgumentException("智付渠道配置不能为空");
        }
        if (request.getSplitRules() == null || request.getSplitRules().isEmpty()) {
            throw new IllegalArgumentException("分账规则不能为空");
        }

        // 构建分账规则JSON字符串
        String splitRuleJson = buildSplitRuleJson(request.getSplitRules());

        return DinPayProfitSharingRequestDTO.builder()
                .interfaceName(DinPaymentEnum.interfaceName.DELAY_PROFIT_SHARING.getName())
                .delayOrderNo(request.getProfitSharingNo())
                .payOrderNo(request.getPaymentNo())
                .payOrderType(request.getPayOrderType())
                .splitRule(splitRuleJson)
                .channelConfig(request.getChannelConfig())
                .build();
    }

    /**
     * 转换分账响应
     *
     * @param channelResponse 智付分账响应
     * @param originalRequest 原始分账请求
     * @return 统一分账结果
     */
    public UnifiedProfitSharingResult convertProfitSharingResponse(net.summerfarm.payment.trade.adapter.dinpay.dto.DinResponseDTO<net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayProfitSharingResponseDTO> channelResponse,
                                                                   UnifiedProfitSharingRequest originalRequest) {
        DinPayProfitSharingResponseDTO responseData = channelResponse.getData();
        ProfitSharingStatus status = convertToProfitSharingStatus(responseData.getSplittingStatus());

        return UnifiedProfitSharingResult.builder()
                .profitSharingNo(responseData.getProfitSharingNo())
                .paymentNo(originalRequest.getPaymentNo())
                .status(status)
                .success(DinPaymentEnum.responseCode.SUCCESS.getCode().equals(channelResponse.getCode()))
                .channelName(getChannelName())
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 构建分账规则JSON字符串
     *
     * @param splitRules 分账规则列表
     * @return JSON字符串
     */
    private String buildSplitRuleJson(List<SplitRule> splitRules) {
        List<Map<String, Object>> ruleList = splitRules.stream()
                .map(rule -> {
                    Map<String, Object> ruleMap = new HashMap<>();
                    ruleMap.put("splitBillMerchantNo", rule.getMerchantNo());
                    ruleMap.put("splitBillAmount", rule.getAmount());
                    return ruleMap;
                })
                .collect(Collectors.toList());

        return JSON.toJSONString(ruleList);
    }

    /**
     * 转换智付分账状态为统一分账状态
     *
     * @param dinPaySharingStatus 智付分账状态
     * @return 统一分账状态
     */
    private ProfitSharingStatus convertToProfitSharingStatus(String dinPaySharingStatus) {
        if (dinPaySharingStatus == null) {
            return ProfitSharingStatus.UNKNOWN;
        }

        switch (dinPaySharingStatus) {
            case "DOING":
                return ProfitSharingStatus.DOING;
            case "SUCCESS":
                return ProfitSharingStatus.SUCCESS;
            case "FAILED":
                return ProfitSharingStatus.FAILED;
            default:
                return ProfitSharingStatus.UNKNOWN;
        }
    }

    // ========== 分账查询相关方法 ==========

    /**
     * 转换分账查询请求
     *
     * @param request 统一分账查询请求
     * @return 智付分账查询请求DTO
     */
    public net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayQueryProfitSharingRequestDTO convertQueryProfitSharingRequest(net.summerfarm.payment.trade.model.request.UnifiedQueryProfitSharingRequest request) {
        if (request.getChannelConfig() == null) {
            throw new IllegalArgumentException("智付渠道配置不能为空");
        }

        return DinPayQueryProfitSharingRequestDTO.builder()
                .interfaceName(DinPaymentEnum.interfaceName.DELAY_PROFIT_SHARING_QUERY.getName())
                .delayOrderNo(request.getProfitSharingNo())
                .channelConfig(request.getChannelConfig())
                .build();
    }

    /**
     * 转换分账查询响应
     *
     * @param channelResponse 智付分账查询响应
     * @param originalRequest 原始分账查询请求
     * @return 统一分账查询结果
     */
    public UnifiedQueryProfitSharingResult convertQueryProfitSharingResponse(net.summerfarm.payment.trade.adapter.dinpay.dto.DinResponseDTO<net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayQueryProfitSharingResponseDTO> channelResponse,
                                                                             UnifiedQueryProfitSharingRequest originalRequest) {
        DinPayQueryProfitSharingResponseDTO responseData = channelResponse.getData();
        ProfitSharingStatus status = convertToProfitSharingStatus(responseData.getDelayStatus());

        // 解析完成时间
        LocalDateTime completeTime = parseCompleteTime(responseData.getCompleteTime());

        // 解析分账结果详情
        List<SplitRuleResult> splitRules = parseSplitRules(responseData.getSplitRules());

        return UnifiedQueryProfitSharingResult.builder()
                .profitSharingNo(responseData.getDelayOrderNo())
                .paymentNo(originalRequest.getPaymentNo())
                .status(status)
                .statusDesc(responseData.getStatusDesc())
                .completeTime(completeTime)
                .splitRules(splitRules)
                .success(DinPaymentEnum.responseCode.SUCCESS.getCode().equals(channelResponse.getCode()))
                .channelName(getChannelName())
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 解析完成时间
     *
     * @param completeTimeStr 完成时间字符串
     * @return LocalDateTime
     */
    private LocalDateTime parseCompleteTime(String completeTimeStr) {
        if (completeTimeStr == null || completeTimeStr.trim().isEmpty()) {
            return null;
        }

        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            return LocalDateTime.parse(completeTimeStr, formatter);
        } catch (Exception e) {
            log.warn("解析完成时间失败: {}", completeTimeStr, e);
            return null;
        }
    }

    /**
     * 解析分账结果详情
     *
     * @param splitRulesJson 分账结果JSON字符串
     * @return 分账结果详情列表
     */
    private List<SplitRuleResult> parseSplitRules(String splitRulesJson) {
        if (splitRulesJson == null || splitRulesJson.trim().isEmpty()) {
            return new ArrayList<>();
        }

        try {
            List<Map> rawList = JSON.parseArray(splitRulesJson, Map.class);
            List<SplitRuleResult> results = new ArrayList<>();
            for (Map rawMap : rawList) {
                @SuppressWarnings("unchecked")
                Map<String, Object> ruleMap = (Map<String, Object>) rawMap;
                results.add(convertToSplitRuleResult(ruleMap));
            }
            return results;
        } catch (Exception e) {
            log.warn("解析分账结果详情失败: {}", splitRulesJson, e);
            return new ArrayList<>();
        }
    }

    /**
     * 转换Map为SplitRuleResult
     *
     * @param ruleMap 分账规则Map
     * @return SplitRuleResult
     */
    private SplitRuleResult convertToSplitRuleResult(Map<String, Object> ruleMap) {
        return SplitRuleResult.builder()
                .splitBillMerchantNo((String) ruleMap.get("splitBillMerchantNo"))
                .splitBillAmount(new BigDecimal(ruleMap.get("splitBillAmount").toString()))
                .splitBillOrderNum((String) ruleMap.get("splitBillOrderNum"))
                .splitBillOrderStatus((String) ruleMap.get("splitBillOrderStatus"))
                .build();
    }

    // ========== 分账回退相关方法 ==========

    /**
     * 转换分账回退请求
     *
     * @param request 统一分账回退请求
     * @return 智付分账回退请求DTO
     */
    public net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayProfitSharingRefundRequestDTO convertProfitSharingRefundRequest(net.summerfarm.payment.trade.model.request.UnifiedProfitSharingRefundRequest request) {
        if (request.getChannelConfig() == null) {
            throw new IllegalArgumentException("智付渠道配置不能为空");
        }
        if (request.getRefundSplitRules() == null || request.getRefundSplitRules().isEmpty()) {
            throw new IllegalArgumentException("分账回退规则不能为空");
        }

        // 构建分账回退规则JSON字符串
        String refundSplitRulesJson = buildRefundSplitRulesJson(request.getRefundSplitRules());

        return DinPayProfitSharingRefundRequestDTO.builder()
                .interfaceName(DinPaymentEnum.interfaceName.DELAY_PROFIT_SHARING_REFUND.getName())
                .refundOrderNo(request.getRefundProfitSharingNo())
                .delayOrderNo(request.getProfitSharingNo())
                .refundSplitRules(refundSplitRulesJson)
                .channelConfig(request.getChannelConfig())
                .build();
    }

    /**
     * 转换分账回退响应
     *
     * @param channelResponse 智付分账回退响应
     * @param originalRequest 原始分账回退请求
     * @return 统一分账回退结果
     */
    public UnifiedProfitSharingRefundResult convertProfitSharingRefundResponse(net.summerfarm.payment.trade.adapter.dinpay.dto.DinResponseDTO<net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayProfitSharingRefundResponseDTO> channelResponse,
                                                                               UnifiedProfitSharingRefundRequest originalRequest) {
        DinPayProfitSharingRefundResponseDTO responseData = channelResponse.getData();
        ProfitSharingRefundStatus status = convertToProfitSharingRefundStatus(responseData.getRefundStatus());

        return UnifiedProfitSharingRefundResult.builder()
                .refundProfitSharingNo(responseData.getRefundOrderNo())
                .profitSharingNo(originalRequest.getProfitSharingNo())
                .status(status)
                .success(DinPaymentEnum.responseCode.SUCCESS.getCode().equals(channelResponse.getCode()))
                .channelName(getChannelName())
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 构建分账回退规则JSON字符串
     *
     * @param refundSplitRules 分账回退规则列表
     * @return JSON字符串
     */
    private String buildRefundSplitRulesJson(List<SplitRule> refundSplitRules) {
        List<Map<String, Object>> ruleList = refundSplitRules.stream()
                .map(rule -> {
                    Map<String, Object> ruleMap = new HashMap<>();
                    ruleMap.put("merchantNo", rule.getMerchantNo());
                    ruleMap.put("refundAmount", rule.getAmount());
                    return ruleMap;
                })
                .collect(Collectors.toList());

        return JSON.toJSONString(ruleList);
    }

    /**
     * 转换智付分账回退状态为统一分账回退状态
     *
     * @param dinPayRefundStatus 智付分账回退状态
     * @return 统一分账回退状态
     */
    private ProfitSharingRefundStatus convertToProfitSharingRefundStatus(String dinPayRefundStatus) {
        if (dinPayRefundStatus == null) {
            return ProfitSharingRefundStatus.UNKNOWN;
        }

        switch (dinPayRefundStatus) {
            case "INIT":
                return ProfitSharingRefundStatus.INIT;
            case "DOING":
                return ProfitSharingRefundStatus.DOING;
            case "SUCCESS":
                return ProfitSharingRefundStatus.SUCCESS;
            case "FAILED":
                return ProfitSharingRefundStatus.FAILED;
            default:
                return ProfitSharingRefundStatus.UNKNOWN;
        }
    }

    // ========== 分账回退查询相关方法 ==========

    /**
     * 转换分账回退查询请求
     *
     * @param request 统一分账回退查询请求
     * @return 智付分账回退查询请求DTO
     */
    public net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayQueryProfitSharingRefundRequestDTO convertQueryProfitSharingRefundRequest(net.summerfarm.payment.trade.model.request.UnifiedQueryProfitSharingRefundRequest request) {
        if (request.getChannelConfig() == null) {
            throw new IllegalArgumentException("智付渠道配置不能为空");
        }

        return DinPayQueryProfitSharingRefundRequestDTO.builder()
                .interfaceName(DinPaymentEnum.interfaceName.DELAY_PROFIT_SHARING_REFUND_QUERY.getName())
                .refundOrderNo(request.getRefundProfitSharingNo())
                .channelConfig(request.getChannelConfig())
                .build();
    }

    /**
     * 转换分账回退查询响应
     *
     * @param channelResponse 智付分账回退查询响应
     * @param originalRequest 原始分账回退查询请求
     * @return 统一分账回退查询结果
     */
    public UnifiedQueryProfitSharingRefundResult convertQueryProfitSharingRefundResponse(DinResponseDTO<net.summerfarm.payment.trade.adapter.dinpay.dto.DinPayQueryProfitSharingRefundResponseDTO> channelResponse,
                                                                                         UnifiedQueryProfitSharingRefundRequest originalRequest) {
        DinPayQueryProfitSharingRefundResponseDTO responseData = channelResponse.getData();
        ProfitSharingRefundStatus status = convertToProfitSharingRefundStatus(responseData.getRefundStatus());

        // 解析完成时间
        LocalDateTime completeTime = parseCompleteTime(responseData.getCompleteTime());

        // 解析分账回退结果详情
        List<RefundSplitRuleResult> refundSplitRules = parseRefundSplitRules(responseData.getRefundSplitRules());

        return UnifiedQueryProfitSharingRefundResult.builder()
                .refundProfitSharingNo(responseData.getRefundOrderNo())
                .status(status)
                .statusDesc(responseData.getStatusDesc())
                .completeTime(completeTime)
                .refundSplitRules(refundSplitRules)
                .success(DinPaymentEnum.responseCode.SUCCESS.getCode().equals(channelResponse.getCode()))
                .channelName(getChannelName())
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 解析分账回退结果详情
     *
     * @param refundSplitRulesJson 分账回退结果JSON字符串
     * @return 分账回退结果详情列表
     */
    private List<RefundSplitRuleResult> parseRefundSplitRules(String refundSplitRulesJson) {
        if (refundSplitRulesJson == null || refundSplitRulesJson.trim().isEmpty()) {
            return new ArrayList<>();
        }

        try {
            List<Map> rawList = JSON.parseArray(refundSplitRulesJson, Map.class);
            List<RefundSplitRuleResult> results = new ArrayList<>();
            for (Map rawMap : rawList) {
                @SuppressWarnings("unchecked")
                Map<String, Object> ruleMap = (Map<String, Object>) rawMap;
                results.add(convertToRefundSplitRuleResult(ruleMap));
            }
            return results;
        } catch (Exception e) {
            log.warn("解析分账回退结果详情失败: {}", refundSplitRulesJson, e);
            return new ArrayList<>();
        }
    }

    /**
     * 转换Map为RefundSplitRuleResult
     *
     * @param ruleMap 分账回退规则Map
     * @return RefundSplitRuleResult
     */
    private RefundSplitRuleResult convertToRefundSplitRuleResult(Map<String, Object> ruleMap) {
        return RefundSplitRuleResult.builder()
                .merchantNo((String) ruleMap.get("merchantNo"))
                .refundAmount(new BigDecimal(ruleMap.get("refundAmount").toString()))
                .refundStatus((String) ruleMap.get("refundStatus"))
                .build();
    }
}
