package com.cosfo.mall.payment;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.trade.model.config.ChannelConfig;

/**
 * 智付分账回退请求DTO
 * 
 * <AUTHOR> Agent
 * @date 2025-08-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DinPayProfitSharingRefundRequestDTO {

    /**
     * 接口名称
     */
    private String interfaceName;

    /**
     * 商户请求分账退回订单号
     */
    private String refundOrderNo;

    /**
     * 原分账请求订单号
     */
    private String delayOrderNo;

    /**
     * 分账退回规则（JSON格式字符串）
     * 格式：[{"merchantNo":"D10000000000001", "refundAmount": 0.01}]
     */
    private String refundSplitRules;

    /**
     * 渠道配置信息（不参与JSON序列化）
     */
    @JSONField(serialize = false, deserialize = false)
    private ChannelConfig channelConfig;
}
