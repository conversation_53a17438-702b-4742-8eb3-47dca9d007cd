package com.cosfo.mall.order.service.impl;

import com.cosfo.mall.order.model.dto.OrderAmountRefreshDTO;
import com.cosfo.mall.order.model.vo.OrderAmountRefreshVO;
import com.cosfo.mall.order.service.OrderService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 订单金额刷新功能测试
 * 测试新的原子性处理逻辑：
 * - 运费刷新：仅处理三方仓订单
 * - 手续费刷新：处理所有待支付订单
 * - 原子性更新：运费变化时同时重新计算手续费，确保金额一致性
 *
 * @author: AI Assistant
 * @date: 2025-01-04
 */
@SpringBootTest
@ActiveProfiles("test")
public class OrderServiceImplRefreshAmountTest {

    @Resource
    private OrderService orderService;

    /**
     * 测试场景1：仅运费变化（三方仓订单）
     */
    @Test
    public void testRefreshOrderAmount_OnlyDeliveryFeeChanged() {
        // 准备测试数据 - 需要包含三方仓订单
        OrderAmountRefreshDTO dto = new OrderAmountRefreshDTO();
        dto.setOrderNoList(Arrays.asList("TEST_THIRD_PARTY_ORDER_001"));

        // 执行测试
        OrderAmountRefreshVO result = orderService.refreshOrderAmount(dto);

        // 验证结果
        if (result.getRefreshFlag()) {
            assertTrue(result.getDeliveryFeeRefreshFlag());
            assertNotNull(result.getNewDeliveryFee());
            assertNotNull(result.getOriDeliveryFee());
            assertNotEquals(result.getOriDeliveryFee(), result.getNewDeliveryFee());
            System.out.println("仅运费变化测试通过");
        } else {
            System.out.println("订单金额未发生变化");
        }
    }

    /**
     * 测试场景2：仅手续费变化（待支付订单）
     */
    @Test
    public void testRefreshOrderAmount_OnlyServiceFeeChanged() {
        // 准备测试数据 - 包含待支付订单
        OrderAmountRefreshDTO dto = new OrderAmountRefreshDTO();
        dto.setOrderNoList(Arrays.asList("TEST_PAYABLE_ORDER_001", "TEST_PAYABLE_ORDER_002"));

        // 执行测试
        OrderAmountRefreshVO result = orderService.refreshOrderAmount(dto);

        // 验证结果
        if (result.getRefreshFlag()) {
            assertTrue(result.getServiceFeeRefreshFlag());
            assertNotNull(result.getNewServiceFee());
            assertNotNull(result.getOriServiceFee());
            assertNotEquals(result.getOriServiceFee(), result.getNewServiceFee());
            System.out.println("仅手续费变化测试通过");
        } else {
            System.out.println("订单金额未发生变化");
        }
    }

    /**
     * 测试场景3：运费和手续费都变化
     */
    @Test
    public void testRefreshOrderAmount_BothChanged() {
        // 准备测试数据 - 包含三方仓订单和其他待支付订单
        OrderAmountRefreshDTO dto = new OrderAmountRefreshDTO();
        dto.setOrderNoList(Arrays.asList("TEST_THIRD_PARTY_ORDER_001", "TEST_PAYABLE_ORDER_001"));

        // 执行测试
        OrderAmountRefreshVO result = orderService.refreshOrderAmount(dto);

        // 验证结果
        if (result.getRefreshFlag()) {
            // 可能运费变化，也可能手续费变化，或者都变化
            assertTrue(result.getDeliveryFeeRefreshFlag() || result.getServiceFeeRefreshFlag());
            System.out.println("运费和手续费变化测试通过");
        } else {
            System.out.println("订单金额未发生变化");
        }
    }

    /**
     * 测试场景4：都没有变化
     */
    @Test
    public void testRefreshOrderAmount_NoChange() {
        // 准备测试数据
        OrderAmountRefreshDTO dto = new OrderAmountRefreshDTO();
        dto.setOrderNoList(Arrays.asList("TEST_NO_CHANGE_ORDER"));

        // 执行测试
        OrderAmountRefreshVO result = orderService.refreshOrderAmount(dto);

        // 验证结果
        assertFalse(result.getRefreshFlag());
        assertFalse(result.getDeliveryFeeRefreshFlag());
        assertFalse(result.getServiceFeeRefreshFlag());
        System.out.println("无变化测试通过");
    }

    /**
     * 测试多订单手续费刷新
     */
    @Test
    public void testMultipleOrdersServiceFeeRefresh() {
        // 准备测试数据 - 多个待支付订单
        OrderAmountRefreshDTO dto = new OrderAmountRefreshDTO();
        dto.setOrderNoList(Arrays.asList("ORDER_001", "ORDER_002", "ORDER_003"));

        // 执行测试
        OrderAmountRefreshVO result = orderService.refreshOrderAmount(dto);

        // 验证结果
        assertNotNull(result.getOriServiceFee());
        assertNotNull(result.getNewServiceFee());

        System.out.println("多订单手续费刷新测试完成");
        System.out.println("原手续费总计: " + result.getOriServiceFee());
        System.out.println("新手续费总计: " + result.getNewServiceFee());
    }

    /**
     * 测试只有非待支付订单的情况
     */
    @Test
    public void testNonPayableOrdersOnly() {
        // 准备测试数据 - 只包含非待支付订单
        OrderAmountRefreshDTO dto = new OrderAmountRefreshDTO();
        dto.setOrderNoList(Arrays.asList("PAID_ORDER_001", "CANCELLED_ORDER_002"));

        // 执行测试
        OrderAmountRefreshVO result = orderService.refreshOrderAmount(dto);

        // 验证结果 - 应该没有手续费变化
        assertFalse(result.getServiceFeeRefreshFlag());
        System.out.println("非待支付订单测试通过");
    }

    /**
     * 测试原子性更新：运费变化导致手续费重新计算
     */
    @Test
    public void testAtomicUpdate_DeliveryFeeAffectsServiceFee() {
        // 准备测试数据 - 三方仓订单，运费变化会影响手续费
        OrderAmountRefreshDTO dto = new OrderAmountRefreshDTO();
        dto.setOrderNoList(Arrays.asList("THIRD_PARTY_ORDER_WITH_SERVICE_FEE"));

        // 执行测试
        OrderAmountRefreshVO result = orderService.refreshOrderAmount(dto);

        // 验证结果
        if (result.getRefreshFlag()) {
            // 运费变化时，手续费应该基于新的订单金额重新计算
            assertTrue(result.getDeliveryFeeRefreshFlag());
            assertNotNull(result.getNewDeliveryFee());
            assertNotNull(result.getNewServiceFee());

            System.out.println("原子性更新测试通过");
            System.out.println("运费变化: " + result.getOriDeliveryFee() + " -> " + result.getNewDeliveryFee());
            System.out.println("手续费重新计算: " + result.getOriServiceFee() + " -> " + result.getNewServiceFee());
        } else {
            System.out.println("订单金额未发生变化");
        }
    }

    /**
     * 测试金额一致性：确保更新过程中不会出现瞬时的金额不一致
     */
    @Test
    public void testAmountConsistency() {
        // 准备测试数据
        OrderAmountRefreshDTO dto = new OrderAmountRefreshDTO();
        dto.setOrderNoList(Arrays.asList("CONSISTENCY_TEST_ORDER"));

        // 执行测试
        OrderAmountRefreshVO result = orderService.refreshOrderAmount(dto);

        // 验证结果 - 确保金额计算的一致性
        if (result.getRefreshFlag()) {
            assertNotNull(result.getOrderAmount());

            // 验证订单总金额 = 商品金额 + 运费 + 手续费
            BigDecimal expectedTotal = BigDecimal.ZERO;
            if (result.getNewDeliveryFee() != null) {
                expectedTotal = expectedTotal.add(result.getNewDeliveryFee());
            }
            if (result.getNewServiceFee() != null) {
                expectedTotal = expectedTotal.add(result.getNewServiceFee());
            }

            System.out.println("金额一致性测试完成");
            System.out.println("订单总金额: " + result.getOrderAmount());
        }
    }
}
